# 内存泄漏修复指南

## 问题描述

程序运行3天左右会出现崩溃，错误信息为 "double free or corruption (out)"，这是典型的C/C++内存管理错误，主要发生在视频流处理相关的代码中。

## 根本原因分析

1. **OpenCV内存管理问题**：
   - `VideoCapture` 对象在多线程环境下的资源释放不安全
   - 帧数据的复制和释放存在竞态条件
   - 长时间运行导致内存碎片化

2. **线程同步问题**：
   - 多个线程同时访问同一个 `VideoCapture` 对象
   - 线程停止时的资源清理顺序不当

3. **资源泄漏**：
   - 视频流连接异常断开时的清理不完整
   - 图像缓存没有及时清理

## 修复方案

### 1. 增强线程安全性

- 使用 `RLock` 替代普通锁，避免死锁
- 为 `VideoCapture` 操作添加专门的锁
- 添加线程停止事件，实现优雅停止

### 2. 改进内存管理

- 安全的帧复制和释放机制
- 添加超时参数防止阻塞
- 增强异常处理和资源清理

### 3. 添加内存监控

- 定期检查内存使用情况
- 自动垃圾回收机制
- 清理过期的图像缓存

### 4. 优化连接池管理

- 改进空闲连接清理逻辑
- 添加强制垃圾回收
- 增强错误处理

## 主要修改内容

### ResilientFrameGrabber 类改进

```python
# 主要改进点：
1. 使用 RLock 和专门的 cap_lock
2. 添加 _stop_event 用于优雅停止
3. 增强 _connect() 方法的资源管理
4. 改进 _update() 方法的内存安全
5. 优化 stop() 方法的清理逻辑
```

### VideoStreamPool 类改进

```python
# 主要改进点：
1. 增强 _close_stream() 的资源清理
2. 在清理线程中添加垃圾回收
3. 改进异常处理
```

### RobotPoolInspectionSystem 类改进

```python
# 主要改进点：
1. 添加内存监控机制
2. 定期清理过期图像缓存
3. 在主循环中集成内存检查
4. 增强错误处理时的垃圾回收
```

## 部署步骤

### 1. 备份现有代码

```bash
cp server/task/robot_pool_inspection.py server/task/robot_pool_inspection.py.backup
```

### 2. 安装依赖

确保安装了 `psutil` 库用于内存监控：

```bash
pip install psutil
```

### 3. 应用修复

修复已经应用到 `server/task/robot_pool_inspection.py` 文件中。

### 4. 测试验证

运行测试脚本验证修复效果：

```bash
python test_memory_fix.py
```

### 5. 监控部署

部署后建议：

1. **监控内存使用**：
   ```bash
   # 监控进程内存使用
   watch -n 60 'ps aux | grep python | grep robot_pool'
   ```

2. **查看日志**：
   ```bash
   # 查看内存相关日志
   tail -f logs/application.log | grep -E "(内存|memory|Memory)"
   ```

3. **设置告警**：
   - 内存使用超过2GB时告警
   - 进程重启时告警

## 配置参数

可以通过配置文件调整以下参数：

```python
# 内存监控配置
memory_check_interval = 300  # 内存检查间隔（秒）
memory_threshold_mb = 1024   # 内存告警阈值（MB）

# 视频流连接池配置
max_idle_time = 300          # 最大空闲时间（秒）
check_interval = 60          # 清理检查间隔（秒）

# 图像缓存配置
max_frame_age = 3600         # 图像最大有效期（秒）
image_buffer_size = 300      # 图像缓存大小
```

## 预期效果

1. **消除内存泄漏**：程序可以稳定运行超过3天
2. **减少崩溃**：消除 "double free or corruption" 错误
3. **提高稳定性**：增强异常处理和资源管理
4. **内存可控**：自动监控和清理机制

## 监控指标

部署后需要监控以下指标：

1. **内存使用趋势**：确保内存使用稳定
2. **进程运行时间**：目标是连续运行7天以上
3. **错误日志**：监控是否还有内存相关错误
4. **性能指标**：确保修复不影响性能

## 故障排除

如果仍然出现问题：

1. **检查日志**：查看详细的错误信息
2. **内存分析**：使用内存分析工具
3. **逐步回滚**：如果问题严重，可以回滚到备份版本
4. **联系支持**：提供详细的错误日志和环境信息

## 注意事项

1. **测试环境验证**：建议先在测试环境验证
2. **逐步部署**：可以先部署到部分服务器
3. **监控告警**：确保监控系统正常工作
4. **备份策略**：保持代码和数据备份

## 后续优化

1. **性能优化**：根据运行情况进一步优化
2. **监控完善**：添加更多监控指标
3. **自动化**：实现自动重启和恢复机制
4. **文档更新**：根据实际运行情况更新文档
