# -------------------------------------------------------- #
#                           日报生成                           #
# -------------------------------------------------------- #
import os 
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from server.utils import pg_tool
from datetime import datetime
import json
from typing import List, Dict, Any, Tuple
from openai import OpenAI
from llms.config_report_day import SYSTEM_REPORT_DAY
from config_file import config
from server.utils.get_cameras_info import get_cameras_config

def preprocess_device_data(data):
    """
    对query_device_today返回的数据进行预处理，将数据分为三组
    
    Args:
        data (list): query_device_today函数返回的数据列表
        
    Returns:
        tuple: 包含三个列表的元组 (warning_data, non_warning_data, transition_data)
            - warning_data: alarm_status为'WARNING'的数据
            - non_warning_data: alarm_status不为'WARNING'的数据
            - transition_data: 从非'WARNING'状态转变为'WARNING'状态的数据
    """
    if not data:
        return [], [], []
    
    # 按时间戳升序排序(确保数据是按时间顺序排列的)
    sorted_data = sorted(data, key=lambda x: x['timestamp'], reverse=False)
    
    warning_data = []
    non_warning_data = []
    transition_data = []
    
    # 上一条记录的告警状态，初始为None
    prev_alarm_status = None
    
    for record in sorted_data:
        current_alarm_status = record.get('alarm_status')
        
        # 分类数据
        if current_alarm_status == 'WARNING':
            warning_data.append(record)
            
            # 检查是否从非WARNING状态转变为WARNING状态
            if prev_alarm_status is not None and prev_alarm_status != 'WARNING':
                transition_data.append(record)
        else:
            non_warning_data.append(record)
        
        # 更新上一条记录的告警状态
        prev_alarm_status = current_alarm_status
    
    return warning_data, non_warning_data, transition_data


def chunk_data(data: List[Dict], chunk_size: int = 5) -> List[List[Dict]]:
    """
    将数据按指定大小分组
    
    Args:
        data (List[Dict]): 要分组的数据列表
        chunk_size (int): 每组的大小，默认为5
        
    Returns:
        List[List[Dict]]: 分组后的数据
    """
    return [data[i:i + chunk_size] for i in range(0, len(data), chunk_size)]


def summarize_normal_data(normal_data: List[Dict]) -> str:
    """
    总结正常数据
    
    Args:
        normal_data (List[Dict]): 正常数据列表
        
    Returns:
        str: 正常数据的总结
    """
    if not normal_data:
        return "今日无正常运行数据记录。"
    
    # 按时间排序确保数据是有序的
    sorted_data = sorted(normal_data, key=lambda x: x['timestamp'])
    
    # 计算实际的正常运行时长
    # 1. 如果记录间隔超过某个阈值(例如5分钟)，则认为这期间可能不是连续正常运行
    time_gap_threshold = 5 * 60  # 5分钟，单位为秒
    
    total_normal_seconds = 0
    normal_periods = []
    
    if len(sorted_data) == 1:
        # 只有一条记录，假设正常运行时间为0
        total_normal_seconds = 0
        normal_periods = [(sorted_data[0]['timestamp'], sorted_data[0]['timestamp'])]
    else:
        # 多条记录，计算连续的正常运行时段
        period_start = sorted_data[0]['timestamp']
        prev_time = period_start
        
        for i in range(1, len(sorted_data)):
            current_time = sorted_data[i]['timestamp']
            time_diff = (current_time - prev_time).total_seconds()
            
            if time_diff > time_gap_threshold:
                # 如果时间间隔过大，记录一个正常运行时段并开始新的时段
                normal_periods.append((period_start, prev_time))
                period_start = current_time
                total_normal_seconds += (prev_time - period_start).total_seconds()
            
            prev_time = current_time
        
        # 添加最后一个时段
        normal_periods.append((period_start, sorted_data[-1]['timestamp']))
        total_normal_seconds += (sorted_data[-1]['timestamp'] - period_start).total_seconds()
    
    # 转换为小时
    total_normal_hours = total_normal_seconds / 3600
    
    # 统计覆盖级别
    coverage_levels = {}
    for record in normal_data:
        level = record.get('coverage_level', 'UNKNOWN')
        coverage_levels[level] = coverage_levels.get(level, 0) + 1
    
    # 提取分析详情和调整建议的主要内容
    analysis_details = set(record.get('analysis_detail', '') for record in normal_data)
    adjustment_suggestions = set(record.get('adjustment_suggestion', '') for record in normal_data)
    
    # 构建摘要
    if len(normal_periods) == 1:
        start_time = normal_periods[0][0]
        end_time = normal_periods[0][1]
        summary = f"设备在{start_time.strftime('%H:%M:%S')}到{end_time.strftime('%H:%M:%S')}期间正常运行，总计约{total_normal_hours:.2f}小时。"
    else:
        # 多个正常运行时段
        period_texts = []
        for start, end in normal_periods:
            period_texts.append(f"{start.strftime('%H:%M:%S')}到{end.strftime('%H:%M:%S')}")
        
        summary = f"设备在多个时间段内正常运行（{', '.join(period_texts)}），总计约{total_normal_hours:.2f}小时。"
    
    # 添加覆盖级别统计
    coverage_info = []
    for level, count in coverage_levels.items():
        percentage = (count / len(normal_data)) * 100
        coverage_info.append(f"{level}级别占比{percentage:.1f}%")
    
    if coverage_info:
        summary += f" 覆盖级别统计：{', '.join(coverage_info)}。"
    
    # 添加分析详情和调整建议的摘要
    if len(analysis_details) == 1 and list(analysis_details)[0]:
        summary += f" 分析详情：{list(analysis_details)[0]}。"
    
    if len(adjustment_suggestions) == 1 and list(adjustment_suggestions)[0]:
        summary += f" 调整建议：{list(adjustment_suggestions)[0]}。"
    
    return summary


def analyze_warning_chunk(warning_chunk: List[Dict]) -> str:
    """
    分析一组告警数据
    
    Args:
        warning_chunk (List[Dict]): 一组告警数据
        
    Returns:
        str: 该组告警数据的分析结果
    """
    if not warning_chunk:
        return ""
    
    # 按时间排序确保数据是有序的
    sorted_data = sorted(warning_chunk, key=lambda x: x['timestamp'])
    
    # 计算告警时间段
    # 如果记录间隔超过某个阈值(例如5分钟)，则认为这是不同的告警时段
    time_gap_threshold = 5 * 60  # 5分钟，单位为秒
    
    warning_periods = []
    
    if len(sorted_data) == 1:
        # 只有一条记录
        warning_periods = [(sorted_data[0]['timestamp'], sorted_data[0]['timestamp'])]
    else:
        # 多条记录，计算连续的告警时段
        period_start = sorted_data[0]['timestamp']
        prev_time = period_start
        
        for i in range(1, len(sorted_data)):
            current_time = sorted_data[i]['timestamp']
            time_diff = (current_time - prev_time).total_seconds()
            
            if time_diff > time_gap_threshold:
                # 如果时间间隔过大，记录一个告警时段并开始新的时段
                warning_periods.append((period_start, prev_time))
                period_start = current_time
            
            prev_time = current_time
        
        # 添加最后一个时段
        warning_periods.append((period_start, sorted_data[-1]['timestamp']))
    
    # 统计覆盖级别
    coverage_levels = {}
    for record in warning_chunk:
        level = record.get('coverage_level', 'UNKNOWN')
        coverage_levels[level] = coverage_levels.get(level, 0) + 1
    
    # 提取分析详情和调整建议
    analysis_details = set(record.get('analysis_detail', '') for record in warning_chunk if record.get('analysis_detail'))
    adjustment_suggestions = set(record.get('adjustment_suggestion', '') for record in warning_chunk if record.get('adjustment_suggestion'))
    
    # 统计故障原因类型
    failure_types = {}
    for record in warning_chunk:
        for failure_type in record.get('failure_reasons_type', []):
            failure_types[failure_type] = failure_types.get(failure_type, 0) + 1
    
    # 构建分析文本
    if len(warning_periods) == 1:
        start_time = warning_periods[0][0]
        end_time = warning_periods[0][1]
        duration_minutes = (end_time - start_time).total_seconds() / 60
        analysis = f"在{start_time.strftime('%H:%M:%S')}到{end_time.strftime('%H:%M:%S')}期间，设备出现告警状态，持续约{duration_minutes:.1f}分钟。"
    else:
        # 多个告警时段
        period_texts = []
        total_duration_minutes = 0
        for start, end in warning_periods:
            duration = (end - start).total_seconds() / 60
            total_duration_minutes += duration
            period_texts.append(f"{start.strftime('%H:%M:%S')}到{end.strftime('%H:%M:%S')}（约{duration:.1f}分钟）")
        
        analysis = f"设备在多个时间段内出现告警状态：{', '.join(period_texts)}，总计约{total_duration_minutes:.1f}分钟。"
    
    # 添加覆盖级别统计
    coverage_info = []
    for level, count in coverage_levels.items():
        percentage = (count / len(warning_chunk)) * 100
        coverage_info.append(f"{level}级别占比{percentage:.1f}%")
    
    if coverage_info:
        analysis += f" 覆盖级别统计：{', '.join(coverage_info)}。"
    
    # 添加故障原因类型统计
    if failure_types:
        main_failures = sorted(failure_types.items(), key=lambda x: x[1], reverse=True)[:3]
        failure_info = [f"{failure_type}({count}次)" for failure_type, count in main_failures]
        analysis += f" 主要故障类型：{', '.join(failure_info)}。"
    
    # 添加分析详情和调整建议的摘要
    if analysis_details:
        analysis += f" 分析详情：{'; '.join(list(analysis_details)[:3])}。"
    
    if adjustment_suggestions:
        analysis += f" 调整建议：{'; '.join(list(adjustment_suggestions)[:3])}。"
    
    return analysis


def analyze_transition_chunk(transition_chunk: List[Dict]) -> str:
    """
    分析一组状态转变数据
    
    Args:
        transition_chunk (List[Dict]): 一组状态转变数据
        
    Returns:
        str: 该组状态转变数据的分析结果
    """
    if not transition_chunk:
        return ""
    
    # 按时间排序
    sorted_data = sorted(transition_chunk, key=lambda x: x['timestamp'])
    
    # 分析转变事件
    results = []
    
    # 统计转变发生的时间分布
    hour_distribution = {}
    coverage_level_counts = {}
    failure_reasons_counts = {}
    
    for record in sorted_data:
        timestamp = record['timestamp']
        hour = timestamp.hour
        hour_distribution[hour] = hour_distribution.get(hour, 0) + 1
        
        coverage_level = record.get('coverage_level', 'UNKNOWN')
        coverage_level_counts[coverage_level] = coverage_level_counts.get(coverage_level, 0) + 1
        
        for reason in record.get('failure_reasons_type', []):
            failure_reasons_counts[reason] = failure_reasons_counts.get(reason, 0) + 1
        
        analysis_detail = record.get('analysis_detail', '无详情')
        adjustment_suggestion = record.get('adjustment_suggestion', '无建议')
        failure_types = ', '.join(record.get('failure_reasons_type', ['未知']))
        
        # 为每个转变事件生成描述
        event_text = f"在{timestamp.strftime('%H:%M:%S')}，设备从正常状态转变为告警状态，"
        event_text += f"覆盖级别为{coverage_level}，"
        
        if failure_types:
            event_text += f"故障类型为{failure_types}，"
        
        event_text += f"分析详情：{analysis_detail}，调整建议：{adjustment_suggestion}。"
        results.append(event_text)
    
    # 生成整体摘要
    summary = ""
    
    # 添加时间分布摘要
    if hour_distribution:
        peak_hours = sorted(hour_distribution.items(), key=lambda x: x[1], reverse=True)[:3]
        peak_hours_text = []
        for hour, count in peak_hours:
            percentage = (count / len(sorted_data)) * 100
            peak_hours_text.append(f"{hour}时({percentage:.1f}%)")
        
        summary += f"状态转变主要发生在以下时段：{', '.join(peak_hours_text)}。"
    
    # 添加覆盖级别摘要
    if coverage_level_counts:
        main_levels = sorted(coverage_level_counts.items(), key=lambda x: x[1], reverse=True)
        level_texts = []
        for level, count in main_levels:
            percentage = (count / len(sorted_data)) * 100
            level_texts.append(f"{level}({percentage:.1f}%)")
        
        summary += f" 状态转变时的覆盖级别分布：{', '.join(level_texts)}。"
    
    # 添加故障原因摘要
    if failure_reasons_counts:
        main_reasons = sorted(failure_reasons_counts.items(), key=lambda x: x[1], reverse=True)[:3]
        reason_texts = []
        for reason, count in main_reasons:
            percentage = (count / len(sorted_data)) * 100
            reason_texts.append(f"{reason}({percentage:.1f}%)")
        
        summary += f" 主要故障原因：{', '.join(reason_texts)}。"
    
    # 组合整体摘要和详细事件
    if summary:
        return summary + "\n\n详细转变事件：\n" + "\n".join(results)
    else:
        return "\n".join(results)


def summarize_with_llm(data_summary: str, data_type: str, system_prompt_type: str) -> str:
    """
    使用大模型对数据进行总结分析
    
    Args:
        data_summary (str): 数据总结
        data_type (str): 数据类型，如'warning'、'normal'、'transition'
        system_prompt_type (str): 系统提示词类型，如'summary'或'all'
        
    Returns:
        str: 大模型生成的总结分析
    """
    # 从配置文件获取API信息
    client = OpenAI(
        api_key=config.env_vars.get("GLM_API_KEY"),
        base_url=config.env_vars.get("GLM_BASE_URL")
    )
    model = config.env_vars.get("GLM_MODEL")
    
    # 获取当前时间
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 根据数据类型构建提示词
    if data_type == 'warning':
        prompt = f"以下是设备告警数据的分析结果，请对这些告警情况进行总结，分析可能的原因和影响，并给出建议：\n\n{data_summary}"
    elif data_type == 'normal':
        prompt = f"以下是设备正常运行数据的分析结果，请对设备正常运行情况进行总结：\n\n{data_summary}"
    elif data_type == 'transition':
        prompt = f"以下是设备从正常状态转变为告警状态的数据分析，请总结这些状态转变的规律、可能原因和影响：\n\n{data_summary}"
    else:
        prompt = f"请对以下设备数据进行分析总结：\n\n{data_summary}"
        
    if system_prompt_type == 'summary':
        system_prompt = SYSTEM_REPORT_DAY.get("system_prompt_report_day_summary", "你是一个专业的设备监控分析师，擅长分析设备运行数据并提供专业的见解。")
    else:
        system_prompt = SYSTEM_REPORT_DAY.get("system_prompt_report_day", "你是一个专业的设备监控分析师，擅长分析设备运行数据并提供专业的见解。")
        # 获取当前生成报告的时间
        report_generation_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        system_prompt = system_prompt + f"\n当前时间: {report_generation_time}"
    try:
        # 调用OpenAI API，使用SYSTEM_REPORT_DAY中的系统提示词
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            # temperature=temperature,
            # max_tokens=max_tokens
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"调用大模型API出错: {e}")
        return f"大模型分析失败: {str(e)}\n原始数据总结: {data_summary}"


def generate_device_report(camera_id, reference_date=None):
    """
    生成设备报告，包含三组数据：告警数据、非告警数据和状态转变数据
    
    Args:
        camera_id (str): 设备ID/摄像头ID
        reference_date (str, optional): 参考日期，格式为'YYYY-MM-DD'，默认为当前日期
        
    Returns:
        dict: 包含三组数据的字典
            - warning_data: alarm_status为'WARNING'的数据
            - non_warning_data: alarm_status不为'WARNING'的数据
            - transition_data: 从非'WARNING'状态转变为'WARNING'状态的数据
    """
    # 如果没有提供参考日期，使用当前日期
    if reference_date is None:
        reference_date = datetime.now().strftime('%Y-%m-%d')
    
    # 获取设备数据
    device_data = pg_tool.query_device_today(camera_id, reference_date)
    
    # 获取摄像头配置信息，查找设备名称
    cameras_config = get_cameras_config()
    device_name = None
    for camera_config in cameras_config:
        if camera_config.get('camera_id') == camera_id:
            device_name = camera_config.get('device_name', '未知设备')
            break
    
    if not device_name:
        device_name = f"设备{camera_id}"
    
    # 预处理数据
    warning_data, non_warning_data, transition_data = preprocess_device_data(device_data)
    
    # 处理正常数据
    normal_summary = summarize_normal_data(non_warning_data)
    normal_analysis = summarize_with_llm(normal_summary, 'normal', 'summary')
    
    # 处理告警数据
    warning_analysis = ""
    if len(warning_data) > 20:
        # 如果告警数据超过20条，按5条一组进行分析
        warning_chunks = chunk_data(warning_data, 5)
        chunk_analyses = []
        
        for i, chunk in enumerate(warning_chunks):
            chunk_analysis = analyze_warning_chunk(chunk)
            chunk_analyses.append(f"告警组 {i+1}:\n{chunk_analysis}")
        
        # 将所有分组分析结果合并
        all_chunk_analyses = "\n\n".join(chunk_analyses)
        
        # 使用大模型对所有分组进行总结
        warning_analysis = summarize_with_llm(all_chunk_analyses, 'warning', 'summary')
    elif warning_data:
        # 如果告警数据不超过20条，直接分析
        warning_summary = analyze_warning_chunk(warning_data)
        warning_analysis = summarize_with_llm(warning_summary, 'warning', 'summary')
    else:
        warning_analysis = "今日无告警数据。"
    
    # 处理状态转变数据
    transition_analysis = ""
    if len(transition_data) > 10:
        # 如果状态转变数据超过10条，按5条一组进行分析
        transition_chunks = chunk_data(transition_data, 5)
        chunk_analyses = []
        
        for i, chunk in enumerate(transition_chunks):
            chunk_analysis = analyze_transition_chunk(chunk)
            chunk_analyses.append(f"状态转变组 {i+1}:\n{chunk_analysis}")
        
        # 将所有分组分析结果合并
        all_chunk_analyses = "\n\n".join(chunk_analyses)
        
        # 使用大模型对所有分组进行总结
        transition_analysis = summarize_with_llm(all_chunk_analyses, 'transition', 'summary')
    elif transition_data:
        # 如果状态转变数据不超过10条，直接分析
        transition_summary = analyze_transition_chunk(transition_data)
        transition_analysis = summarize_with_llm(transition_summary, 'transition', 'summary')
    else:
        transition_analysis = "今日无状态转变数据。"
    
    # 生成完整报告
    report = {
        "raw_data": {
            "warning_data": warning_data,
            "non_warning_data": non_warning_data,
            "transition_data": transition_data
        },
        "analysis": {
            "normal_analysis": normal_analysis,
            "warning_analysis": warning_analysis,
            "transition_analysis": transition_analysis
        },
        "summary": {
            "normal_count": len(non_warning_data),
            "warning_count": len(warning_data),
            "transition_count": len(transition_data)
        },
        "device_info": {
            "camera_id": camera_id,
            "device_name": device_name
        }
    }
    
    return report


def generate_daily_report(camera_id, reference_date=None):
    """
    生成每日报告的完整文本
    
    Args:
        camera_id (str): 设备ID/摄像头ID
        reference_date (str, optional): 参考日期，格式为'YYYY-MM-DD'，默认为当前日期
        
    Returns:
        str: 格式化的每日报告文本
    """
    # 获取报告数据
    report = generate_device_report(camera_id, reference_date)
    
    # 获取设备名称
    device_name = report['device_info']['device_name']
    
    # 格式化日期
    if reference_date is None:
        reference_date = datetime.now().strftime('%Y-%m-%d')
    
    # 构建报告文本
    report_text = f"# 设备 {camera_id} 日报 ({reference_date}) 巡检场景: {device_name}\n\n"
    
    # 添加摘要信息
    report_text += "## 数据摘要\n\n"
    report_text += f"- 正常记录数: {report['summary']['normal_count']}\n"
    report_text += f"- 告警记录数: {report['summary']['warning_count']}\n"
    report_text += f"- 状态转变记录数: {report['summary']['transition_count']}\n\n"
    
    # 添加正常运行分析
    report_text += "## 正常运行分析\n\n"
    report_text += f"{report['analysis']['normal_analysis']}\n\n"
    
    # 添加告警分析
    report_text += "## 告警分析\n\n"
    report_text += f"{report['analysis']['warning_analysis']}\n\n"
    
    # 添加状态转变分析
    report_text += "## 状态转变分析\n\n"
    report_text += f"{report['analysis']['transition_analysis']}\n\n"
    
    # 将设备名称添加到提示词中
    report_text_export = summarize_with_llm(report_text, 'export', 'all')
    
    # 如果report_text_export前后有```符号，则去除
    if report_text_export.startswith('```') and report_text_export.endswith('```'):
        report_text_export = report_text_export[3:-3]
    elif report_text_export.startswith('```\n') and report_text_export.endswith('\n```'):
        report_text_export = report_text_export[4:-4]
        
    normal_count = report['summary']['normal_count']
    warning_count = report['summary']['warning_count']
    transition_count = report['summary']['transition_count']
    normal_analysis = report['analysis']['normal_analysis']
    warning_analysis = report['analysis']['warning_analysis']
    transition_analysis = report['analysis']['transition_analysis']
    
    # 去除normal_analysis、warning_analysis和transition_analysis前后的```符号
    for analysis in [normal_analysis, warning_analysis, transition_analysis]:
        if isinstance(analysis, str):
            if analysis.startswith('```') and analysis.endswith('```'):
                index = [i for i, x in enumerate([normal_analysis, warning_analysis, transition_analysis]) if x == analysis][0]
                if index == 0:
                    normal_analysis = analysis[3:-3]
                elif index == 1:
                    warning_analysis = analysis[3:-3]
                elif index == 2:
                    transition_analysis = analysis[3:-3]
            elif analysis.startswith('```\n') and analysis.endswith('\n```'):
                index = [i for i, x in enumerate([normal_analysis, warning_analysis, transition_analysis]) if x == analysis][0]
                if index == 0:
                    normal_analysis = analysis[4:-4]
                elif index == 1:
                    warning_analysis = analysis[4:-4]
                elif index == 2:
                    transition_analysis = analysis[4:-4]
    
    return device_name, normal_count, warning_count, transition_count, normal_analysis, warning_analysis, transition_analysis, report_text_export


if __name__ == "__main__":
    # 使用当前日期作为参考日期
    current_date = datetime.now().strftime('%Y-%m-%d')
    print(f"使用参考日期: {current_date}")
    
    # 生成报告
    camera_id = "4052"
    # report = generate_device_report(camera_id, current_date)
    
    # print(f"告警数据数量: {report['summary']['warning_count']}")
    # print(f"非告警数据数量: {report['summary']['normal_count']}")
    # print(f"状态转变数据数量: {report['summary']['transition_count']}")
    
    # 生成并打印每日报告
    device_name, normal_count, warning_count, transition_count, normal_analysis, warning_analysis, transition_analysis, daily_report = generate_daily_report(camera_id, current_date)
    print("\n" + "="*50 + "\n")
    print(daily_report)
    print(f"设备名称: {device_name}")
    print(f"正常记录数: {normal_count}")
    print(f"告警记录数: {warning_count}")
    print(f"状态转变记录数: {transition_count}")
    print(f"正常运行分析: {normal_analysis}")
    print(f"告警分析: {warning_analysis}")
    print(f"状态转变分析: {transition_analysis}")