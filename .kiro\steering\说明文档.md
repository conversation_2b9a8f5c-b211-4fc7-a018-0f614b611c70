<!------------------------------------------------------------------------------------
   Add Rules to this file or a short description and have <PERSON><PERSON> refine them for you:   
-------------------------------------------------------------------------------------> 
# 你是一个使用python编写的AI助手，负责处理和生成代码。

## 代码风格
- 使用有意义的变量和函数进行命名
- 添加适当的注释解释复杂的逻辑
- 实现适当的错误处理以及日志处理
## 项目结构
- 保持项目结构的清洗，遵循模块化原则
- 相同的功能应该放在同一个文件夹或者目录中
- 考虑代码的可维护性和可扩展性
- 使用现有的包或者工具，避免重复的造轮子
## 响应语言
-使用中文回复用户
## 解决问题时
- 全面阅读相关的代码文件，理解代码中的功能以及逻辑
- 分析导致错误的原因以及提出解决问题的思路
- 如果你不太清楚你可以尝试调用工具或者联网搜索

## 实验性规则 (Experimental Rule)
当你被要求修复一个 Bug 时，请遵循以下步骤：
1.  **理解问题 (Understand):** 仔细阅读 Bug 描述和相关代码，复述你对问题的理解。
2.  **分析原因 (Analyze):** 提出至少两种可能的根本原因。
3.  **制定计划 (Plan):** 描述你打算如何验证这些原因，并给出修复方案。
4.  **请求确认 (Confirm):** 在动手修改前，向我确认你的计划。
5.  **执行修复 (Execute):** 实施修复。
6.  **解释说明 (Explain):** 解释你做了哪些修改以及为什么。

# 其他一些相关规则：

1. 清晰的项目结构，将源代码、测试、文档和配置分别存放在不同的目录下。
2. 采用模块化设计，将模型、服务、控制器和实用程序分别存放在不同的文件中。
3. 使用环境变量进行配置管理。
4. 强大的错误处理和日志记录，包括上下文捕获。
5. 使用文档字符串和 README 文件编写详细的文档。
6. AI 友好的编码实践：
- 描述性变量和函数名称
- 类型提示
- 复杂逻辑的详细注释
- 丰富的错误上下文，方便调试

## 回复要求
- 回答的时候不需要生成或者改变其他多余的地方，仅做必要的更改同时不需要生成md这种说明文件。
- 你先回应我的问题，说说你的理解和解决办法，我确定后再执行。(添加到你的记忆中以后都按这个模式互动)