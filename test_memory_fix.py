#!/usr/bin/env python3
"""
内存泄漏修复测试脚本
用于验证视频流处理的内存管理改进
"""

import time
import threading
import psutil
import gc
import logging
from datetime import datetime
from server.task.robot_pool_inspection import ResilientFrameGrabber, VideoStreamPool

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def monitor_memory(duration_minutes=30, check_interval=60):
    """
    监控内存使用情况
    
    Args:
        duration_minutes: 监控持续时间（分钟）
        check_interval: 检查间隔（秒）
    """
    logger.info(f"开始内存监控，持续 {duration_minutes} 分钟")
    
    start_time = time.time()
    end_time = start_time + (duration_minutes * 60)
    
    memory_readings = []
    
    while time.time() < end_time:
        try:
            # 获取当前进程内存使用
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            memory_readings.append({
                'timestamp': datetime.now(),
                'memory_mb': memory_mb,
                'memory_percent': process.memory_percent()
            })
            
            logger.info(f"内存使用: {memory_mb:.1f} MB ({process.memory_percent():.1f}%)")
            
            # 如果内存使用过高，记录警告
            if memory_mb > 1024:  # 超过1GB
                logger.warning(f"内存使用过高: {memory_mb:.1f} MB")
                
            time.sleep(check_interval)
            
        except Exception as e:
            logger.error(f"监控内存时出错: {e}")
            time.sleep(check_interval)
    
    # 分析内存使用趋势
    if len(memory_readings) > 1:
        initial_memory = memory_readings[0]['memory_mb']
        final_memory = memory_readings[-1]['memory_mb']
        max_memory = max(reading['memory_mb'] for reading in memory_readings)
        
        logger.info(f"内存监控结果:")
        logger.info(f"  初始内存: {initial_memory:.1f} MB")
        logger.info(f"  最终内存: {final_memory:.1f} MB")
        logger.info(f"  最大内存: {max_memory:.1f} MB")
        logger.info(f"  内存增长: {final_memory - initial_memory:.1f} MB")
        
        if final_memory > initial_memory * 1.5:
            logger.warning("检测到可能的内存泄漏！")
        else:
            logger.info("内存使用正常")

def test_video_stream_pool():
    """测试视频流连接池的内存管理"""
    logger.info("开始测试视频流连接池")
    
    # 创建连接池
    pool = VideoStreamPool(max_idle_time=60, check_interval=30)
    pool.start()
    
    try:
        # 模拟多个RTSP连接（使用测试URL）
        test_urls = [
            "rtsp://admin:test@192.168.1.100/stream1",
            "rtsp://admin:test@192.168.1.101/stream2",
            "rtsp://admin:test@192.168.1.102/stream3"
        ]
        
        # 循环创建和使用连接
        for i in range(100):  # 模拟100次操作
            for url in test_urls:
                try:
                    grabber = pool.get_stream(url)
                    if grabber:
                        # 模拟读取帧
                        ret, frame = grabber.read()
                        logger.info(f"第 {i+1} 次操作，URL: {url}, 读取结果: {ret}")
                    
                    # 每10次操作强制垃圾回收
                    if i % 10 == 0:
                        gc.collect()
                        
                except Exception as e:
                    logger.error(f"测试连接时出错: {e}")
            
            time.sleep(1)  # 短暂休眠
            
    finally:
        pool.stop()
        logger.info("视频流连接池测试完成")

def test_frame_grabber():
    """测试单个帧抓取器的内存管理"""
    logger.info("开始测试帧抓取器")
    
    # 使用测试RTSP URL
    test_url = "rtsp://admin:test@192.168.1.100/stream1"
    
    grabber = ResilientFrameGrabber(test_url)
    
    try:
        grabber.start()
        
        # 模拟长时间运行
        for i in range(300):  # 5分钟，每秒一次
            try:
                ret, frame = grabber.read()
                if i % 60 == 0:  # 每分钟记录一次
                    logger.info(f"第 {i+1} 次读取，结果: {ret}")
                    
                # 每30秒强制垃圾回收
                if i % 30 == 0:
                    gc.collect()
                    
            except Exception as e:
                logger.error(f"读取帧时出错: {e}")
            
            time.sleep(1)
            
    finally:
        grabber.stop()
        logger.info("帧抓取器测试完成")

def main():
    """主测试函数"""
    logger.info("开始内存泄漏修复验证测试")
    
    # 启动内存监控线程
    monitor_thread = threading.Thread(
        target=monitor_memory,
        args=(30, 30),  # 监控30分钟，每30秒检查一次
        daemon=True
    )
    monitor_thread.start()
    
    try:
        # 测试视频流连接池
        test_video_stream_pool()
        
        # 等待一段时间
        time.sleep(60)
        
        # 测试单个帧抓取器
        test_frame_grabber()
        
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中出错: {e}", exc_info=True)
    finally:
        # 强制垃圾回收
        gc.collect()
        logger.info("测试完成")

if __name__ == "__main__":
    main()
