import logging
import os
import time  # 添加time模块导入
from io import BytesIO
from pathlib import Path
from typing import Dict
import re
import cv2
import numpy as np
import requests
from PIL import Image
from dotenv import load_dotenv
from openai import OpenAI
from config_file import config as config_video
from llms.config import SYSTEM
from llms.config_filter import SYSTEM_FILTER
from llms.config_aerobic import SYSTEM_AEROBIC
from llms.vllm_api_server import process_image
from llms.vllm_api_server_multiple import process_image_multiple
from llms.llm_api_server import llm_process_image_filter, llm_process_image_aerobic
from llms.yolo_api_server import DeviceDetector
from llms.yolo_region import YoloRegionCounter
# from server.task.water_level_comparison import RobotWaterLevelMonitor
"""
视频帧处理模块

主要功能：
1. 处理视频帧图像
2. 分析泡沫覆盖率
3. 生成分析报告和建议
4. 管理图片存储
"""


class VideoFrameProcessor:
    """视频帧处理器类
    
    负责处理视频帧并进行智能分析
    
    Attributes:
        base_dataset_path (Path): 数据集基础路径
        openai_client (OpenAI): OpenAI客户端实例
        openai_model (str): 使用的模型名称
        coverage_thresholds (dict): 覆盖率阈值配置
        coverage_level_names (dict): 覆盖等级名称配置
    """

    def __init__(self, config: dict):
        """初始化视频帧处理器
        
        Args:
            config (dict): 配置字典，包含：
                - storage.paths.base_dataset: 数据集路径
                - coverage_levels: 覆盖率相关配置
        
        Raises:
            EnvironmentError: 环境变量加载失败时
            RuntimeError: GLM客户端初始化失败时
        """
        # 获取当前文件所在目录的父目录（项目根目录）
        project_root = Path(__file__).parent.parent.parent

        # 加载环境变量
        env_path = project_root / config_video.args.env_file
        if not load_dotenv(env_path):
            raise EnvironmentError("无法加载环境变量文件")

        # 验证必要的环境变量
        required_vars = ['GLM_API_KEY', 'GLM_BASE_URL', 'GLM_MODEL',
                         'QWEN2_VL_API_KEY', 'QWEN2_VL_BASE_URL', 'QWEN2_VL_MODEL']
        missing_vars = [var for var in required_vars if not config_video.env_vars.get(var)]
        if missing_vars:
            raise EnvironmentError(f"缺少必要的环境变量: {', '.join(missing_vars)}")

        self.base_dataset_path = Path(config['storage']['paths']['base_dataset'])
        
        # 从配置中获取是否保存中间图像的设置
        self.save_intermediate_images = config.get('storage', {}).get('image_processing', {}).get('save_intermediate_images', False)
        logging.info(f"中间处理图像保存设置: {'启用' if self.save_intermediate_images else '禁用'}")

        # 初始化OpenAI客户端
        try:
            self.openai_client = OpenAI(
                api_key=config_video.env_vars.get('GLM_API_KEY'),
                base_url=config_video.env_vars.get('GLM_BASE_URL')
            )
            self.openai_model = config_video.env_vars.get('GLM_MODEL')
        except Exception as e:
            raise RuntimeError(f"初始化GLM客户端失败: {str(e)}")

        # 添加覆盖率阈值的配置
        self.coverage_thresholds = config.get('coverage_levels', {}).get('thresholds', {
            'low': 25,
            'medium': 50,
            'high': 100
        })
        self.coverage_level_names = config.get('coverage_levels', {}).get('levels', {
            'low': 'LOW',
            'medium': 'MEDIUM',
            'high': 'HIGH'
        })
        self.region_points = {
        "region-01": [(160, 300), (600, 300), (600, 0), (160, 0)],  # 向右移动100像素
        "region-02": [(600, 300), (1100, 300), (1100, 0), (600, 0)],  # 向右移动100像素
        "region-03": [(1100, 300), (1500, 300), (1500, 0), (1100, 0)],  # 向右移动100像素
        }
        # 创建检测器实例
        self.detector = DeviceDetector()
        self.regioncounter = YoloRegionCounter(
            # image_path=image_path, # 不填写代表的是不使用预定的图像文件
            region_points=self.region_points,
            model_path="llms/models/yolo/best.pt",
            show=False  # 在无界面环境下设为False
        )
        self.yolo_model = config.get('yolo_model', 'llms/models/yolo/best.pt')
        
        # 初始化滤池YOLO检测器
        from llms.yolo_api_server_filter import DeviceDetector as FilterDetector
        self.filter_detector = FilterDetector(model_path=config.get('yolo_model_filter', 'llms/models/yolo/best-filter.pt'))
        logging.info("初始化滤池检测器完成")
        
        # 设置检测模式：可选 "frame"(仅帧数), "time"(仅时间), "both"(两者)
        self.detection_mode = config.get('detection_mode', 'both')  # 默认使用两者结合的判断模式
        logging.info(f"初始化检测模式: {self.detection_mode} (frame=仅帧数, time=仅时间, both=两者结合)")
        
        # 为每个区域添加计时器和状态跟踪
        self.region_state = {
            "region-01": {"last_state": None, "timer": 0, "alarm_triggered": False, "last_change_time": None},
            "region-02": {"last_state": None, "timer": 0, "alarm_triggered": False, "last_change_time": None},
            "region-03": {"last_state": None, "timer": 0, "alarm_triggered": False, "last_change_time": None}
        }
        # 设置耙斗卡住判断阈值（单位：帧数）
        self.stuck_threshold = config.get('stuck_threshold', 30)  # 默认30帧
        # 设置耙斗卡住判断的时间阈值（单位：秒）
        self.stuck_time_threshold = config.get('stuck_time_threshold', 3.0)  # 默认3秒
        
        # 重置机制配置
        self.reset_enabled = config.get('reset_enabled', True)  # 默认启用重置机制
        # 设置重置阈值，超过此阈值后重置检测状态（单位：帧数）
        self.reset_threshold = config.get('reset_threshold', 120) if self.reset_enabled else False  # 默认120帧
        # 设置重置的时间阈值（单位：秒）
        self.reset_time_threshold = config.get('reset_time_threshold', 12.0) if self.reset_enabled else False  # 默认12秒
        
        # 添加设备倾斜状态跟踪
        self.device_incline_state = {
            1: {"alarm_triggered": False, "last_status": "unknown", "last_change_time": None},
            2: {"alarm_triggered": False, "last_status": "unknown", "last_change_time": None},
            3: {"alarm_triggered": False, "last_status": "unknown", "last_change_time": None}
        }
        # 添加报警重置计数器 - 分开控制倾斜和卡住
        self.incline_reset_counter = 0
        self.stuck_reset_counter = 0
        # 添加报警重置的时间记录
        self.last_incline_reset_time = time.time()
        self.last_stuck_reset_time = time.time()
        
        # 获取是否启用各种重置功能的配置
        # 倾斜检测重置配置
        self.incline_reset_enabled = config.get('incline_reset_enabled', 
                                              config.get('alarm_reset_enabled', True))  # 默认启用
        self.incline_reset_threshold = config.get('incline_reset_threshold', 
                                              config.get('alarm_reset_threshold', 300))  # 默认阈值300帧
        # 倾斜检测重置的时间阈值（单位：秒）
        self.incline_reset_time_threshold = config.get('incline_reset_time_threshold', 
                                                   config.get('alarm_reset_time_threshold', 30.0))  # 默认30秒
        
        # 卡住检测重置配置
        self.stuck_reset_enabled = config.get('stuck_reset_enabled', 
                                            config.get('alarm_reset_enabled', True))  # 默认启用
        self.stuck_reset_threshold = config.get('stuck_reset_threshold', 
                                              config.get('alarm_reset_threshold', 300))  # 默认阈值300帧
        # 卡住检测重置的时间阈值（单位：秒）
        self.stuck_reset_time_threshold = config.get('stuck_reset_time_threshold', 
                                                 config.get('alarm_reset_time_threshold', 30.0))  # 默认30秒
        
        # 保留旧的参数用于兼容
        self.alarm_reset_enabled = config.get('alarm_reset_enabled', True)  # 默认启用
        self.alarm_reset_threshold = config.get('alarm_reset_threshold', 300)  # 默认阈值300帧
        self.alarm_reset_time_threshold = config.get('alarm_reset_time_threshold', 30.0)  # 默认30秒
        
        logging.info(f"初始化报警重置机制: 倾斜检测重置(启用={self.incline_reset_enabled}, 帧阈值={self.incline_reset_threshold}帧, 时间阈值={self.incline_reset_time_threshold}秒), "
                    f"卡住检测重置(启用={self.stuck_reset_enabled}, 帧阈值={self.stuck_reset_threshold}帧, 时间阈值={self.stuck_reset_time_threshold}秒)")

        # 存储每个摄像头的上一次泡沫面积
        self.previous_bubble_area = {}
        # 存储每个摄像头的泡沫面积增加次数
        self.bubble_increase_counter = {}
        # 设置连续增加触发报警的阈值
        self.bubble_increase_threshold = config.get('bubble_increase_threshold', 2)  # 默认连续3次


    def process_frame_filter(self, frame, frame_count, save_dir, camera_id, sensor_data,
                      threshold, system_type, standard_image_path=None, current_time=None) -> tuple:
        """处理单个视频帧并返回分析结果
        
        Args:
            frame (np.ndarray): 视频帧图像数据
            frame_count (int): 帧计数
            save_dir (Path): 保存目录
            camera_id (str): 摄像头ID
            sensor_data (Dict): 传感器数据
            threshold (float): 覆盖率阈值
            system_type (str): 系统类型
            standard_image_path (str, optional): 标准图片路径
            current_time (datetime, optional): 当前时间
            
        Returns:
            tuple: (覆盖率, 分析结果, 警报状态, 是否异常, 图片路径, 分析建议)
        """
        frame_filename = f"frame_{camera_id}_{current_time.strftime('%Y_%m_%d_%H_%M_%S')}.jpg"
        frame_path = save_dir / frame_filename
        failure_reasons_type = [] # 出现故障的类型
        # 调整图像尺寸为1600x900
        resized_frame = cv2.resize(frame, (1600, 900), interpolation=cv2.INTER_AREA)

        # 保存调整后的帧
        cv2.imwrite(str(frame_path), resized_frame)


        # 处理标准图像（因为设及一个摄像头进行多个任务的识别）
        if system_type == 'system_prompt_bucket_shaft':
            # 修改耙斗井去除栅条损坏检测
            system_prompts = ['system_prompt_waste_percentage' ,'system_prompt_bulky_waste' ]
            response_dict = {}
            for prompt in system_prompts:
                # 默认不保存中间处理图像
                response = self._process_image_comparison(resized_frame, frame_path, standard_image_path, prompt)
                response_dict[prompt] = response
        elif system_type == 'system_prompt_bucket_shaft_multiple': # 多图耙斗井
            system_prompts = ['system_prompt_waste_percentage_multiple' ,'system_prompt_bulky_waste_multiple' ]
            response_dict = {}
            for prompt in system_prompts:
                # 默认不保存中间处理图像
                response = self._process_image_comparison(resized_frame, frame_path, standard_image_path, prompt)
                response_dict[prompt] = response
        else: # 一个设备只进行一个任务的识别
            # 默认不保存中间处理图像，以节省存储空间
            # 如需在测试环境保存中间图像，可修改save_intermediate_images参数为True
            response_dict = self._process_image_comparison(
                resized_frame, frame_path, standard_image_path, system_type
            )
        
        # ********************************** 获取分析结果 ********************************** #
        # 根据系统类型进行不同的处理逻辑
        if "filter" in system_type.lower():
            # analysis_result = llm_process_image_filter(filter_result=response_dict.get('情况分析', ''), system_type=system_type)
            # value = response_dict['反冲洗是否均匀']
            # print(f"---------------------------")
            # print(f"反冲洗是否均匀: {value}")
            # print(f"---------------------------")
            # if value =='不均匀':
            #     coverage_float = 99
            #     failure_reasons_type.append('反冲洗不均匀')
            # else:
            #     coverage_float = 1
            # all_situation_analysis = response_dict.get('情况分析', '')
            analysis_result = llm_process_image_filter(filter_result=response_dict.get('你的思考', ''), system_type=system_type)
            value = response_dict['曝气头是否脱落或损坏']
            print(f"---------------------------")
            print(f"曝气头是否脱落或损坏: {value}")
            print(f"---------------------------")
            if value =='是':
                coverage_float = 99
                failure_reasons_type.append('曝气头可能出现脱落或损坏')
            else:
                coverage_float = 1
            all_situation_analysis = response_dict.get('你的思考', '')
        # 好氧池
        elif system_type in ['system_prompt_aerobic_single1', 'system_prompt_aerobic_multiple1', 'system_prompt_aerobic_single2', 'system_prompt_aerobic_multiple2']:
            # 分析泡沫覆盖率
            value = response_dict['当前泡沫覆盖率']
            number = re.search(r'\d+(\.\d+)?', value)
            if number is None:
                logging.warning(f"无法从泡沫覆盖率中提取数字: {value}")
                coverage_float = 0
            else:
                coverage_float = float(number.group(0))
            # 曝气头脱落
            down_air_bubble = response_dict.get('曝气头是否脱落或者损坏', '否')
            
            # 获取传感器数据
            sensor_data_do = sensor_data.get('DO', '-')
            sensor_data_mlss = sensor_data.get('MLSS', '-')
            if sensor_data_do == '-' and sensor_data_mlss == '-':
                analysis_result = "暂无法提供详细建议,字段信息不全或者出现异常值,请检查DO和MLSS的数值"
                all_situation_analysis = response_dict.get('情况分析', '')
            else:
                analysis_result = llm_process_image_aerobic(filter_result=response_dict.get('情况分析', ''), DO=sensor_data.get('DO'), MLSS=sensor_data.get('MLSS'), bubble_area=coverage_float, system_type=system_type)
                all_situation_analysis = response_dict.get('情况分析', '')
                
                # 获取上一次记录的泡沫面积并检查是否有增长
                previous_area = self.previous_bubble_area.get(camera_id, 0)
                
                # 添加各种异常情况提示信息
                situation_changes = []
                
                # 检查曝气头脱落状态
                if down_air_bubble == '有':
                    analysis_result += "\n警告：检测到曝气头脱落或损坏，需要立即进行检修。"
                    situation_changes.append("检测到曝气头脱落或损坏，需要立即进行检修")
                    failure_reasons_type.append('曝气头脱落或损坏')
                
                # 检查泡沫面积增长情况
                if previous_area > 0 and coverage_float > previous_area:
                    # 初始化增加计数器（如果不存在）
                    if camera_id not in self.bubble_increase_counter:
                        self.bubble_increase_counter[camera_id] = 0
                    
                    # 增加计数
                    self.bubble_increase_counter[camera_id] += 1
                    
                    # 如果连续增加次数达到阈值
                    if self.bubble_increase_counter[camera_id] >= self.bubble_increase_threshold:
                        # 添加泡沫面积增长信息到分析结果
                        analysis_result += f"\n注意：泡沫面积连续{self.bubble_increase_counter[camera_id]}次增加，最近从 {previous_area}% 增加到 {coverage_float}%，可能表明工艺状况正在恶化，建议及时检查。"
                        situation_changes.append(f"泡沫面积连续增长情况：当前 {coverage_float}%，上次 {previous_area}%，增长了 {coverage_float - previous_area}%")
                        failure_reasons_type.append('泡沫面积增长')
                    else:
                        # 记录增长但还未达到报警阈值
                        logging.info(f"摄像头 {camera_id} 泡沫面积增加 {self.bubble_increase_counter[camera_id]}/{self.bubble_increase_threshold} 次: {previous_area}% -> {coverage_float}%")
                else:
                    # 如果没有增加，重置计数器
                    self.bubble_increase_counter[camera_id] = 0
                
                # 将变化情况添加到情况分析
                if situation_changes:
                    all_situation_analysis += "\n\n异常状况摘要：\n- " + "\n- ".join(situation_changes)
        # 青苔检测
        elif "system_prompt_moss" in system_type.lower(): 
            value = response_dict['是否为青苔']
            if value == '是':
                coverage_float = 99
                
                failure_reasons_type.append('出现青苔')
            else:
                coverage_float = 1
            analysis_result = response_dict.get('处理建议', '识别到青苔,正在处理中...')
            all_situation_analysis = response_dict.get('图片分析结果', '')

        # 泡沫检测
        elif "system_prompt_slag_outlet" in system_type.lower() and "system_prompt_slag_outletv2" not in system_type.lower():
            value = response_dict['泡沫状况评估']
            if value == '过多':
                coverage_float = 99
            else:
                coverage_float = 1
            analysis_result = response_dict['处理建议']
            all_situation_analysis = response_dict.get('给出你的判断依据', '')
        # 二沉池整体视角分析
        elif "system_prompt_holistic_perspective" in system_type.lower():
            logging.info(f"处理二沉池整体视角分析，系统类型: {system_type}")
            print(f"---------------------------")
            print(f"二沉池分析响应: {response_dict}")
            print(f"---------------------------")
            
            value = response_dict.get('是否需要报警', '否')
            if value == '是':
                coverage_float = 99
                failure_reasons_type.append('二沉池水面异常')
                logging.warning(f"检测到二沉池浮渣异常，触发报警")
            else:
                coverage_float = 1
                logging.info(f"二沉池浮渣状况正常")
                
            analysis_result = response_dict.get('处理建议', '暂无相关建议')
            all_situation_analysis = response_dict.get('可能原因分析', '暂无相关建议')
            
            # 记录详细的分析结果
            logging.info(f"二沉池分析 - 浮渣类型: {response_dict.get('浮渣类型', '未知')}, "
                        f"分布范围: {response_dict.get('浮渣分布范围', '未知')}, "
                        f"密度评估: {response_dict.get('浮渣密度评估', '未知')}, "
                        f"异常程度: {response_dict.get('异常程度', '未知')}")
        # 树叶识别
        elif "system_prompt_leaf_recognition" in system_type.lower():
            logging.info(f"处理树叶识别分析，系统类型: {system_type}")
            print(f"---------------------------")
            print(f"树叶识别响应: {response_dict}")
            print(f"---------------------------")
            
            value = response_dict.get('是否需要报警', '否')
            if value == '是':
                coverage_float = 99
                failure_reasons_type.append('树叶堆积过多')
                logging.warning(f"检测到树叶堆积过多，触发报警")
            else:
                coverage_float = 1
                logging.info(f"树叶堆积状况正常")
                
            analysis_result = response_dict.get('处理建议', '')
            all_situation_analysis = response_dict.get('风险评估', '')
            
            # 记录树叶覆盖面积
            leaf_coverage = response_dict.get('树叶覆盖面积百分比', '0%')
            logging.info(f"树叶识别 - 覆盖面积: {leaf_coverage}")
        # 排浮渣识别
        elif "system_prompt_slag_outletv2" in system_type.lower():
            logging.info(f"处理排浮渣识别分析，系统类型: {system_type}")
            print(f"---------------------------")
            print(f"排浮渣识别响应: {response_dict}")
            print(f"---------------------------")
            
            value = response_dict.get('是否需要报警', '否')
            if value == '是':
                coverage_float = 99
                failure_reasons_type.append('撇渣管浮渣堆积')
                logging.warning(f"检测到撇渣管浮渣堆积，触发报警")
            else:
                coverage_float = 1
                logging.info(f"撇渣管浮渣状况正常")
                
            analysis_result = response_dict.get('处理建议', '')
            all_situation_analysis = response_dict.get('风险评估', '')
            
            # 记录浮渣覆盖面积
            slag_coverage = response_dict.get('浮渣覆盖面积百分比', '0%')
            logging.info(f"撇渣管分析 - 浮渣覆盖面积: {slag_coverage}, 浮渣类型: {response_dict.get('浮渣类型', '未知')}")
        # 耙斗
        elif "system_prompt_bucket_dipper" in system_type.lower():
            
            devices_list = response_dict['devices'] # 是否倾斜
            region_counts = response_dict['region_counts'] # 是否卡住
            
            # 记录设备列表和区域计数的详细日志
            logging.info(f"帧 {frame_count} - 检测到的设备: {devices_list}")
            logging.info(f"帧 {frame_count} - 区域计数: {region_counts}")
            
            # 获取当前时间
            current_process_time = time.time()
            
            # 处理倾斜检测重置 - 根据检测模式选择判断条件
            should_reset_incline = False
            if self.detection_mode == 'frame':
                # 仅使用帧数判断
                should_reset_incline = self.incline_reset_counter >= self.incline_reset_threshold
            elif self.detection_mode == 'time':
                # 仅使用时间判断
                should_reset_incline = (current_process_time - self.last_incline_reset_time) >= self.incline_reset_time_threshold
            else:  # 'both' 模式或其他情况
                # 使用帧数或时间判断(两者满足其一即可)
                should_reset_incline = (self.incline_reset_counter >= self.incline_reset_threshold or 
                                      (current_process_time - self.last_incline_reset_time) >= self.incline_reset_time_threshold)
            
            if self.incline_reset_enabled and should_reset_incline:
                # 只重置倾斜检测相关的报警状态
                for device_id in self.device_incline_state:
                    self.device_incline_state[device_id]["alarm_triggered"] = False
                self.incline_reset_counter = 0
                self.last_incline_reset_time = current_process_time
                logging.info(f"倾斜检测报警状态已重置，允许再次触发报警 (时间差: {current_process_time - self.last_incline_reset_time:.2f}秒)")
            
            # 处理卡住检测重置 - 根据检测模式选择判断条件
            should_reset_stuck = False
            if self.detection_mode == 'frame':
                # 仅使用帧数判断
                should_reset_stuck = self.stuck_reset_counter >= self.stuck_reset_threshold
            elif self.detection_mode == 'time':
                # 仅使用时间判断
                should_reset_stuck = (current_process_time - self.last_stuck_reset_time) >= self.stuck_reset_time_threshold
            else:  # 'both' 模式或其他情况
                # 使用帧数或时间判断(两者满足其一即可)
                should_reset_stuck = (self.stuck_reset_counter >= self.stuck_reset_threshold or 
                                    (current_process_time - self.last_stuck_reset_time) >= self.stuck_reset_time_threshold)
            
            if self.stuck_reset_enabled and should_reset_stuck:
                # 只重置卡住检测相关的报警状态
                for region_name in self.region_state:
                    self.region_state[region_name]["alarm_triggered"] = False
                self.stuck_reset_counter = 0
                self.last_stuck_reset_time = current_process_time
                logging.info(f"卡住检测报警状态已重置，允许再次触发报警 (时间差: {current_process_time - self.last_stuck_reset_time:.2f}秒)")
            
            coverage_float = 1  # 默认设置为1
            status_analysis = []
            stuck_regions = []
            new_alarms = False  # 标记是否有新的报警
            actual_device_status = 1  # 新增变量：跟踪设备实际状态，1=正常，99=异常
            
            # 初始化跟踪新报警类型的变量
            has_new_incline_alarm = False  # 是否有新的倾斜报警
            has_new_stuck_alarm = False    # 是否有新的卡住报警
            
            # 遍历所有设备检查倾斜状态
            has_inclined_devices = False  # 标记是否有倾斜的设备
            
            # 获取当前帧检测到的设备ID列表
            detected_device_ids = [device['device_id'] for device in devices_list]
            
            # 处理未被检测到的设备 - 保持最后一次观察到的状态，而不是重置
            for device_id in self.device_incline_state:
                if device_id not in detected_device_ids:
                    # 如果设备未被检测到，只记录日志，不改变其状态
                    last_status = self.device_incline_state[device_id]["last_status"]
                    logging.info(f"设备 {device_id} 未在当前帧中检测到，保持其最后观察到的状态: {last_status}")
                    
                    # 记录设备暂时不可见
                    if not hasattr(self.device_incline_state[device_id], "invisible_frames"):
                        self.device_incline_state[device_id]["invisible_frames"] = 1
                    else:
                        self.device_incline_state[device_id]["invisible_frames"] += 1
                    
                    # 只记录连续不可见超过一定帧数的情况
                    if self.device_incline_state[device_id].get("invisible_frames", 0) % 30 == 0:
                        logging.warning(f"设备 {device_id} 已连续 {self.device_incline_state[device_id]['invisible_frames']} 帧未被检测到")
            
            # 遍历所有检测到的设备
            for device in devices_list:
                device_id = device['device_id']
                status = device['status']
                confidence = device['confidence']
                
                # 记录每个设备的状态日志
                logging.info(f"帧 {frame_count} - 设备 {device_id}: 状态={status}, 置信度={confidence:.2f}")
                
                # 确保设备ID存在于状态字典中
                if device_id not in self.device_incline_state:
                    self.device_incline_state[device_id] = {
                        "alarm_triggered": False, 
                        "last_status": "unknown",
                        "invisible_frames": 0,
                        "last_change_time": current_process_time  # 初始化时间戳
                    }
                
                # 重置不可见帧计数器，因为设备现在可见
                self.device_incline_state[device_id]["invisible_frames"] = 0
                
                # 获取设备的上一次状态
                last_status = self.device_incline_state[device_id]["last_status"]
                
                # 检查是否有倾斜设备
                if status == 'incline':
                    # 设置实际设备状态为异常
                    actual_device_status = 99
                    has_inclined_devices = True
                    
                    # 如果之前未触发报警或者从正常状态变回倾斜状态，则标记为新报警
                    if not self.device_incline_state[device_id]["alarm_triggered"] or (last_status == "no_incline" and status == "incline"):
                        new_alarms = True
                        has_new_incline_alarm = True  # 标记有新的倾斜报警
                        self.device_incline_state[device_id]["alarm_triggered"] = True
                        self.device_incline_state[device_id]["last_change_time"] = current_process_time  # 记录状态变化时间
                        logging.warning(f"设备 {device_id} 倾斜，触发报警 (上一状态: {last_status})")
                else:
                    # 如果设备从倾斜恢复到正常状态，重置报警状态
                    if last_status == "incline" and status == "no_incline":
                        self.device_incline_state[device_id]["alarm_triggered"] = False
                        self.device_incline_state[device_id]["last_change_time"] = current_process_time  # 记录状态变化时间
                        logging.info(f"设备 {device_id} 从倾斜恢复到正常状态，重置报警状态")
                
                # 更新设备上一次状态
                self.device_incline_state[device_id]["last_status"] = status
                
                # 根据设备ID确定位置描述
                if device_id == 1:
                    position = "左边"
                elif device_id == 2:
                    position = "中间"
                elif device_id == 3:
                    position = "右边"
                else:
                    position = f"设备{device_id}"
                status_analysis.append(f"{position}耙斗状态为{status}")
            # 在遍历完所有设备后，检查所有设备状态（包括不可见的设备）
            # 统计有多少设备处于倾斜状态（包括暂时不可见但上次状态为倾斜的设备）
            inclined_device_count = 0
            inclined_device_ids = []
            
            for device_id, state in self.device_incline_state.items():
                # 如果设备最后一次观察到的状态是倾斜，或者已经触发了报警且未被重置
                if state["last_status"] == "incline" or state["alarm_triggered"]:
                    inclined_device_count += 1
                    inclined_device_ids.append(device_id)
                    has_inclined_devices = True  # 确保任何倾斜设备都会被标记
            
            # 记录详细的状态信息
            if inclined_device_count > 0:
                logging.warning(f"当前共有 {inclined_device_count} 个耙斗处于倾斜状态 (设备ID: {inclined_device_ids})")
                failure_reasons_type.append('耙斗倾斜')
                
            # 检查每个区域是否卡住
            has_stuck_region = False  # 标记是否有卡住的区域
            
            # 记录原始区域计数信息
            print(f"---------------------------")
            print(f"原始区域计数: {region_counts}")
            logging.info(f"帧 {frame_count} - 原始区域计数信息: {region_counts}")
            print(f"---------------------------")
            
            # 检查是否有区域不在结果中，这可能表示数据问题
            missing_regions = set(self.region_state.keys()) - set(region_counts.keys())
            if missing_regions:
                print(f"---------------------------")
                print(f"警告：以下区域在检测结果中缺失: {missing_regions}")
                logging.warning(f"帧 {frame_count} - 以下区域在region_counts中缺失: {missing_regions}")
                print(f"---------------------------")
            
            # 遍历所有预定义的区域，而不是只遍历region_counts中的区域
            for region_name in self.region_state:
                # 获取区域计数，如果region_counts中不存在该区域，则视为0（无耙斗）
                count = region_counts.get(region_name, 0)
                region_state = self.region_state[region_name]
                
                # 如果区域在region_counts中缺失，记录特殊日志
                if region_name not in region_counts:
                    print(f"---------------------------")
                    print(f"区域 {region_name} 在检测结果中缺失，假定值为0（无耙斗）")
                    logging.warning(f"帧 {frame_count} - 区域 {region_name} 在检测结果中缺失，假定值为0（无耙斗）")
                    print(f"---------------------------")
                
                # 记录每个区域当前状态的详细日志
                print(f"---------------------------")
                logging.info(f"帧 {frame_count} - 区域 {region_name}: count={count}, timer={region_state['timer']}, last_state={region_state['last_state']}, alarm_triggered={region_state['alarm_triggered']}")
                print(f"---------------------------")
                
                # 首次运行时初始化状态
                if region_state["last_state"] is None:
                    region_state["last_state"] = count
                    region_state["last_change_time"] = current_process_time  # 初始化时间戳
                    print(f"---------------------------")
                    logging.info(f"区域 {region_name} 初始化状态为 {count}")
                    print(f"---------------------------")
                    continue
                
                # 从1变为0时开始计时
                if region_state["last_state"] == 1 and count == 0:
                    # 如果是状态首次变化，记录变化时间
                    if region_state["timer"] == 0:
                        region_state["last_change_time"] = current_process_time
                    
                    region_state["timer"] += 1
                    time_diff = current_process_time - region_state["last_change_time"]
                    logging.info(f"区域 {region_name} 从有耙斗变为无耙斗，计时器增加到 {region_state['timer']}，已经过时间: {time_diff:.2f}秒")
                # 如果从0变为1或保持1，重置计时器
                elif count == 1:
                    if region_state["timer"] > 0:
                        logging.info(f"区域 {region_name} 检测到耙斗，计时器从 {region_state['timer']} 重置为 0")
                    region_state["timer"] = 0
                    region_state["last_change_time"] = current_process_time  # 重置状态变化时间
                    # 添加重置报警状态的逻辑，让耙斗重新出现后可以再次触发报警
                    if region_state["alarm_triggered"]:
                        region_state["alarm_triggered"] = False
                        logging.info(f"区域 {region_name} 检测到耙斗重新出现，报警状态已重置，允许再次触发报警")
                # 如果一直是0，且已经开始计时，继续计时
                elif region_state["last_state"] == 0 and count == 0 and region_state["timer"] > 0:
                    region_state["timer"] += 1
                    time_diff = current_process_time - region_state["last_change_time"]
                    logging.info(f"区域 {region_name} 持续无耙斗，计时器增加到 {region_state['timer']}，已经过时间: {time_diff:.2f}秒")
                
                # 检查是否启用了重置机制且超过重置阈值 - 根据检测模式选择判断条件
                should_reset_region = False
                if self.detection_mode == 'frame':
                    # 仅使用帧数判断
                    should_reset_region = region_state["timer"] >= self.reset_threshold
                elif self.detection_mode == 'time':
                    # 仅使用时间判断
                    should_reset_region = (region_state["last_change_time"] is not None and 
                                         current_process_time - region_state["last_change_time"] >= self.reset_time_threshold)
                else:  # 'both' 模式或其他情况
                    # 使用帧数或时间判断(两者满足其一即可)
                    should_reset_region = (region_state["timer"] >= self.reset_threshold or 
                                         (region_state["last_change_time"] is not None and 
                                          current_process_time - region_state["last_change_time"] >= self.reset_time_threshold))
                
                if self.reset_enabled and self.reset_threshold and should_reset_region:
                    mode_str = "帧计数" if self.detection_mode == 'frame' else ("时间" if self.detection_mode == 'time' else "帧计数或时间")
                    logging.info(f"区域 {region_name} 基于{mode_str}判断达到重置阈值 (计时器={region_state['timer']}, 时间={current_process_time - region_state['last_change_time']:.2f}秒)，重新开始检测")
                    region_state["timer"] = 0
                    region_state["last_state"] = count
                    region_state["last_change_time"] = current_process_time
                    continue
                    
                # 更新状态
                region_state["last_state"] = count
                
                # 打印当前区域状态信息，用于调试
                print(f"区域 {region_name}: count={count}, timer={region_state['timer']}, last_state={region_state['last_state']}")
                
                # 检查是否超过卡住阈值 - 根据检测模式选择判断条件
                is_stuck = False
                if self.detection_mode == 'frame':
                    # 仅使用帧数判断
                    is_stuck = region_state["timer"] >= self.stuck_threshold
                elif self.detection_mode == 'time':
                    # 仅使用时间判断
                    is_stuck = (region_state["last_change_time"] is not None and 
                              current_process_time - region_state["last_change_time"] >= self.stuck_time_threshold)
                else:  # 'both' 模式或其他情况
                    # 使用帧数或时间判断(两者满足其一即可)
                    is_stuck = (region_state["timer"] >= self.stuck_threshold or
                              (region_state["last_change_time"] is not None and 
                               current_process_time - region_state["last_change_time"] >= self.stuck_time_threshold))
                
                if is_stuck:
                    # 检查是否应该触发报警（考虑重置机制）
                    should_alarm = False
                    
                    # 计算是否处于重置状态
                    is_reset_state = False
                    if self.reset_enabled:
                        if self.detection_mode == 'frame':
                            is_reset_state = self.reset_threshold and region_state["timer"] >= self.reset_threshold
                        elif self.detection_mode == 'time':
                            is_reset_state = self.reset_time_threshold and region_state["last_change_time"] is not None and current_process_time - region_state["last_change_time"] >= self.reset_time_threshold
                        else:  # 'both' 模式
                            is_reset_state = (self.reset_threshold and region_state["timer"] >= self.reset_threshold) or \
                                           (self.reset_time_threshold and region_state["last_change_time"] is not None and \
                                            current_process_time - region_state["last_change_time"] >= self.reset_time_threshold)
                    
                    # 如果不处于重置状态，则允许报警
                    should_alarm = not is_reset_state
                    
                    if should_alarm:
                        # 设置实际设备状态为异常
                        actual_device_status = 99
                        # 只要区域卡住就添加到故障类型列表中，无论是否首次触发
                        if not has_stuck_region:  # 只添加一次"耙斗卡住"到故障类型列表
                            failure_reasons_type.append('耙斗卡住')
                            has_stuck_region = True
                            logging.warning(f"检测到耙斗卡住状态，添加到故障类型列表")
                        # 只有当该区域之前未触发报警时才标记为新报警
                        if not region_state["alarm_triggered"]:
                            stuck_regions.append(region_name)
                            region_state["alarm_triggered"] = True
                            has_new_stuck_alarm = True  # 标记有新的卡住报警
                            new_alarms = True
                            time_diff = current_process_time - region_state["last_change_time"] if region_state["last_change_time"] is not None else 0
                            logging.warning(f"区域 {region_name} 卡住，首次触发报警，计时器值为 {region_state['timer']}，已经过时间: {time_diff:.2f}秒")
                        else:
                            # 区域已经报警过，但仍然需要记录在stuck_regions中用于显示
                            stuck_regions.append(region_name)
                            time_diff = current_process_time - region_state["last_change_time"] if region_state["last_change_time"] is not None else 0
                            logging.info(f"区域 {region_name} 仍处于卡住状态，已报警过，计时器值为 {region_state['timer']}，已经过时间: {time_diff:.2f}秒")
            
            # 根据新的报警类型设置 coverage_float
            if has_new_incline_alarm or has_new_stuck_alarm:
                coverage_float = 99  # 只有新报警才设置 coverage_float 为 99
                
                # 记录详细的报警源信息
                alarm_sources = []
                if has_new_incline_alarm:
                    alarm_sources.append("新的耙斗倾斜报警")
                if has_new_stuck_alarm:
                    alarm_sources.append("新的耙斗卡住报警")
                logging.info(f"设置 coverage_float=99，报警源: {', '.join(alarm_sources)}")
            
            # 生成分析结果字符串
            if not status_analysis:
                base_analysis = "当前未识别到任何耙斗设备"
                logging.warning("当前未识别到任何耙斗设备")
            else:
                # 将英文状态转换为中文描述
                status_analysis_translated = []
                for analysis in status_analysis:
                    analysis = analysis.replace("no_incline", "没有倾斜")
                    analysis = analysis.replace("incline", "倾斜")
                    status_analysis_translated.append(analysis)
                base_analysis = "从左到右当前的" + "，".join(status_analysis_translated)
                logging.info(f"设备状态分析: {base_analysis}")
            
            if stuck_regions:
                # 将区域编号转换为更直观的位置描述
                region_descriptions = []
                for region in stuck_regions:
                    if region == "region-01":
                        region_desc = "区域1(左边区域)"
                    elif region == "region-02":
                        region_desc = "区域2(中间区域)"
                    elif region == "region-03":
                        region_desc = "区域3(右边区域)"
                    else:
                        region_desc = region.replace("region-0", "区域")
                    region_descriptions.append(region_desc)
                
                stuck_regions_text = "、".join(region_descriptions)
                logging.info(f"卡住区域: {stuck_regions_text}")
                
                # 根据是否为新报警来调整情况分析的描述，并添加详细的报警源信息
                alarm_source_detail = []
                if has_new_incline_alarm:
                    alarm_source_detail.append("耙斗倾斜")
                if has_new_stuck_alarm:
                    alarm_source_detail.append(f"{stuck_regions_text}耙斗卡住")
                
                if new_alarms:
                    # 组合所有新报警源
                    if alarm_source_detail:
                        new_alarm_text = f"新检测到的异常: {', '.join(alarm_source_detail)}"
                    else:
                        new_alarm_text = f"检测到{stuck_regions_text}可能发生耙斗卡住！"
                    
                    all_situation_analysis = f"{base_analysis}。{new_alarm_text}"
                    logging.warning(f"新报警: {all_situation_analysis}")
                else:
                    all_situation_analysis = f"{base_analysis}。{stuck_regions_text}仍有耙斗卡住状态，但已发出报警提示。"
                    logging.info(f"持续报警: {all_situation_analysis}")
            else:
                all_situation_analysis = base_analysis
                if coverage_float == 1:  # 只有在没有其他报警时记录正常状态
                    logging.info(f"正常状态: {all_situation_analysis}")
            
            # 更新分析建议
            if actual_device_status == 99:  # 使用actual_device_status判断设备实际状态
                # 已经在上面的代码中统计了所有倾斜的设备（包括不可见但状态保持的设备）
                # 此处 has_inclined_devices 已经包含了完整的倾斜设备信息
                
                if new_alarms:  # 有新报警
                    # 构建分析建议，明确区分不同类型的新报警
                    suggestion_parts = []
                    
                    # 处理卡住报警
                    if has_new_stuck_alarm and stuck_regions:
                        # 同样为分析建议中的区域添加位置描述
                        region_descriptions = []
                        for region in stuck_regions:
                            if region == "region-01":
                                region_desc = "区域1(左边区域)"
                            elif region == "region-02":
                                region_desc = "区域2(中间区域)"
                            elif region == "region-03":
                                region_desc = "区域3(右边区域)"
                            else:
                                region_desc = region.replace("region-0", "区域")
                            region_descriptions.append(region_desc)
                        
                        suggestion_parts.append(f"新检测到{'、'.join(region_descriptions)}的耙斗可能卡住")
                    
                    # 处理倾斜报警
                    if has_new_incline_alarm:
                        suggestion_parts.append("新检测到耙斗倾斜")
                    
                    # 如果没有新报警但有持续的报警，也要处理
                    if not suggestion_parts:
                        if stuck_regions and not has_new_stuck_alarm:
                            suggestion_parts.append("耙斗卡住状态持续")
                        if has_inclined_devices and not has_new_incline_alarm:
                            suggestion_parts.append("耙斗倾斜状态持续")
                    
                    # 组合最终建议
                    if suggestion_parts:
                        analysis_result = "、".join(suggestion_parts) + "。请运维人员尽快前往现场对设备运行状态进行评估，并依据流程展开处理。"
                    else:
                        # 以防万一有新报警但未被上面的逻辑捕获
                        analysis_result = "检测到设备异常，请运维人员尽快前往现场对设备运行状态进行评估。"
                else:  # 无新报警但仍有异常状态
                    # 构建持续异常状态的分析建议
                    ongoing_issues = []
                    
                    # 处理持续的卡住状态
                    if stuck_regions:
                        region_descriptions = []
                        for region in stuck_regions:
                            if region == "region-01":
                                region_desc = "区域1(左边区域)"
                            elif region == "region-02":
                                region_desc = "区域2(中间区域)"
                            elif region == "region-03":
                                region_desc = "区域3(右边区域)"
                            else:
                                region_desc = region.replace("region-0", "区域")
                            region_descriptions.append(region_desc)
                        
                        ongoing_issues.append(f"{'、'.join(region_descriptions)}仍有耙斗卡住状态")
                    
                    # 处理持续的倾斜状态
                    if has_inclined_devices:
                        ongoing_issues.append("设备仍有倾斜状态")
                    
                    # 组合最终建议
                    if ongoing_issues:
                        analysis_result = "、".join(ongoing_issues) + "，已发出报警提示"
                    else:
                        analysis_result = "所有设备运行正常"
                
                logging.info(f"分析建议: {analysis_result}")
            else:
                analysis_result = "所有设备运行正常"
                logging.info("分析结果: 所有设备运行正常")
        # 耙斗井
        elif "system_prompt_bucket_shaft" in system_type.lower():
            result_string = '视觉模型的分析结果为:'
            analysis_result = '调整建议为:'
            for i, (key, value) in enumerate(response_dict.items()):
                
                # analysis_result = analysis_result + '\n' + value['调整建议']
                if i == 0:
                    items = list(value.items())[:-1]  # 排除最后一个键值对
                    result_string = result_string + '\n' + f"垃圾占比检测结果: {'; '.join([f'{k}: {v}' for k, v in items])}"
                    last_key, last_value = list(value.items())[-1]  # 获取最后一个键值对
                    analysis_result = analysis_result + '\n' + f"垃圾占比调整建议:{last_value}"
                    # result_string = result_string + '\n' + f"垃圾占比检测结果: {', '.join([f'{k}: {v}' for k, v in value.items()])}"
                    value1 = value['垃圾占比']
                    number = re.search(r'\d+(\.\d+)?', value1)
                    if number is None:
                        logging.warning(f"无法从垃圾占比中提取数字: {value1}")
                        value1 = 0
                    else:
                        value1 = float(number.group(0))
                    value1, is_abnormal = self._determine_alarm_status(value1, threshold)
                elif i == 1:
                    items = list(value.items())[:-1]  # 排除最后一个键值对
                    result_string = result_string + '\n' + f"大件垃圾检测结果: {'; '.join([f'{k}: {v}' for k, v in items])}"
                    last_key, last_value = list(value.items())[-1]  # 获取最后一个键值对
                    analysis_result = analysis_result + '\n' + f"大件垃圾调整建议:{last_value}"
                    # result_string = result_string + '\n' + f"大件垃圾检测结果: {', '.join([f'{k}: {v}' for k, v in value.items()])}"
                    value2 = value['是否有大件垃圾']
            
            if value1 == 'WARNING' or value2 == '有' :
                coverage_float = 99
                if value1 == 'WARNING':
                    failure_reasons_type.append('垃圾占比过高')
                if value2 == '有':
                    failure_reasons_type.append('大件垃圾')
            else:
                coverage_float = 1
            
            analysis_result = analysis_result
            all_situation_analysis = result_string
        # ********************************** 确定警报状态 ********************************** #
        # 确定警报状态
        if system_type in ['system_prompt_aerobic_single1', 'system_prompt_aerobic_multiple1', 'system_prompt_aerobic_single2', 'system_prompt_aerobic_multiple2']: # 好氧池的根据其他状态判断报警信息
            # 获取上一次记录的泡沫面积
            previous_area = self.previous_bubble_area.get(camera_id, 0)
            
            # 初始化警报状态和异常标志
            alarm_status_flag = 0
            is_abnormal = False
            
            # 检查曝气头脱落状态，无论泡沫面积情况如何，曝气头脱落都需要触发报警
            if down_air_bubble == '有':
                alarm_status_flag = 1
                is_abnormal = True
                logging.info(f"摄像头 {camera_id} 检测到曝气头脱落，触发报警")
            
            # 检查泡沫面积连续增长情况，即使曝气头已经触发了报警，也需要检查并记录泡沫面积变化
            if self.bubble_increase_counter.get(camera_id, 0) >= self.bubble_increase_threshold:
                alarm_status_flag = 1
                is_abnormal = True
                logging.info(f"摄像头 {camera_id} 泡沫面积连续{self.bubble_increase_counter[camera_id]}次增加，触发报警")
            
            # 更新上一次泡沫面积记录
            self.previous_bubble_area[camera_id] = coverage_float
        else: # 根据覆盖面积判断报警信息（除了大坦沙的以往的都是使用泡沫面积，下一版本优化掉）
            alarm_status_flag, is_abnormal = self._determine_alarm_status(coverage_float, threshold)
        
        return (coverage_float, all_situation_analysis, alarm_status_flag, is_abnormal,
                str(frame_path), analysis_result,failure_reasons_type) # analysis_result表示的是建议,表示的是返回的识别故障类型
# ******************************** 分析摄像头取出的帧图像 *******************************<<< #
    def _process_image_comparison(self, frame, frame_path, standard_image_path, system_type, save_intermediate_images=False):
        """处理图像比较
        
        Args:
            frame: 当前帧图像
            frame_path: 帧图像保存路径
            standard_image_path: 标准图像路径
            system_type: 系统类型
            
        Returns:
            dict: 图像分析结果
        """
        # 为撇渣管浮渣堆积识别添加绘制框线的处理
        if "system_prompt_slag_outletv2" in system_type.lower():
            # 保存原始图像
            cv2.imwrite(str(frame_path), frame)
            
            # 获取图像尺寸
            height, width = frame.shape[:2]
            
            # 定义感兴趣区域的坐标 - 这里使用相对位置，可以根据实际需要调整
            # 默认在图像下方区域绘制矩形框，这是撇渣管通常所在的位置
            x1, y1 = int(width * 0.1), int(height * 0.6)  # 左上角坐标
            x2, y2 = int(width * 0.9), int(height * 0.9)  # 右下角坐标
            
            # 在图像上绘制矩形框
            color = (0, 0, 255)  # 红色 (BGR格式)
            thickness = 2
            frame_with_box = frame.copy()
            cv2.rectangle(frame_with_box, (x1, y1), (x2, y2), color, thickness)
            
            # 保存带框线的图像
            frame_with_box_path = str(frame_path).replace('.jpg', '_with_box.jpg')
            cv2.imwrite(frame_with_box_path, frame_with_box)
            
            logging.info(f"为撇渣管浮渣堆积识别绘制框线，保存到: {frame_with_box_path}")
            
            # 使用带框线的图像进行分析
            process_image_result = process_image(frame_with_box_path, system_type)
            return process_image_result
            
        if standard_image_path: # 有标准图片使用多图的识别逻辑
            standard_image_filename = Path(standard_image_path).name
            local_image_path = self._save_standard_image(standard_image_path, standard_image_filename)
            frames = [cv2.imread(str(local_image_path)), frame]
            print(f"---------------------------")
            print(f"多图提示词是：: {system_type}")
            print(f"---------------------------")
            return process_image_multiple(frames, system_type)
        elif system_type == 'system_prompt_bucket_dipper':
            # 检查是否在测试模式下使用测试图片
            # if hasattr(self, 'test_mode') and self.test_mode and hasattr(self, 'current_test_image_index'):
            #     # 使用测试图片
            #     test_image_path = self.test_images[self.current_test_image_index]
            #     print(f"---------------------------")
            #     print(f"使用测试图片: {test_image_path}")
            #     print(f"---------------------------")
            #     image = cv2.imread(test_image_path)
            #     self.current_test_image_index = (self.current_test_image_index + 1) % len(self.test_images)
            # else:
            image = frame  # 使用帧numpy格式的数据处理
            
            regioncounter = YoloRegionCounter(
                region_points=self.region_points,
                model_path="llms/models/yolo/best.pt",
                show=False  # 在无界面环境下设为False
            )
            results = self.detector.detect_devices(image, save_result=False)
            print(f"---------------------------")
            print(f"yolo识别结果是: {results}")
            print(f"---------------------------")
            
            try:
                image_results = regioncounter.process_frame(image, save_output=False)
                print(f"---------------------------")
                print('区域计数结果:', image_results)
                print(f"---------------------------")
            finally:
                regioncounter._close_regioncounter()
                print("图像识别完成，资源已释放")
            
            # 合并YOLO检测结果和区域计数结果
            combined_results = {
                'devices': results['devices'],
                'region_counts': image_results.region_counts,
                'total_tracks': image_results.total_tracks
            }
         
            return combined_results
        else: 
            # 没有标准图片使用单图的识别逻辑
            if system_type in ['system_prompt_filter_multiple']:
                # 使用YOLO模型进行滤池识别
                
                # 调用滤池YOLO检测器
                results_path = self.filter_detector.detect_devices(frame, save_result=False)
                logging.info(f"---------------------------")
                logging.info(f"滤池YOLO识别结果: {results_path}")
                logging.info(f"---------------------------")
                
                # 解析YOLO检测结果
                devices = results_path.get('devices', [])
                has_malfunction = any(device.get('status') == 'malfunction' for device in devices)
                
                # 根据检测结果生成返回值
                if has_malfunction:
                    process_image_result = {
                        "你的思考": "根据判定规则,需要识别的图片中存在局部凸起水体，符合曝气头脱落或损坏的判定标准。",
                        "是否反冲洗": "是",
                        "曝气头是否脱落或损坏": "是",
                        "调整建议": "1. 建议暂停滤池反冲洗流程，检查凸起区域对应的曝气头；\n2. 更换脱落或破损的曝气头，并确保重新固定牢固；\n3. 重启滤池，观察水面翻滚是否均匀。"
                    }
                else:
                    process_image_result = {
                        "你的思考": "根据判定规则，需要识别的监控中没有曝气头出现故障",
                        "是否反冲洗": "否",
                        "曝气头是否脱落或损坏": "否",
                        "调整建议": "无需调整"
                    }
                # 以下是原有的大模型代码，现在注释保留
                """
                # 使用assets中的滤池基准图像作为参考
                base_image = cv2.imread("assets/filter_aeration_failure_images.png")
                if base_image is None:
                    logging.error("无法加载基准图像：assets/filter_aeration_failure_images.png")
                    return {"错误": "无法加载基准图像"}
                # 构建包含基准图像和当前图像的列表
                frames = [base_image, frame]
                process_image_result = process_image_multiple(frames, system_type)
                """
                return process_image_result
            else:
                # 这里需要将曝气头脱离以及泡沫面积拆分出来,然后分别调用两次模型
                if system_type in ['system_prompt_aerobic_single1', 'system_prompt_aerobic_single2']:
                    # 根据不同的系统类型，选择不同的提示词及坐标文件进行处理
                    air_bubble_frame_path = None
                    bubble_area_frame_path = None
                    
                    # 保存原始图像，为透视变换和分割做准备
                    original_frame_path = str(frame_path)
                    
                    # 创建处理后图像的保存路径
                    processed_dir = Path(original_frame_path).parent / 'processed'
                    processed_dir.mkdir(parents=True, exist_ok=True)
                    
                    # 获取原始文件名和扩展名
                    original_filename = Path(original_frame_path).stem
                    original_ext = Path(original_frame_path).suffix
                    
                    # 根据系统类型选择不同的坐标文件和处理方法
                    if system_type == 'system_prompt_aerobic_single1':
                        # 前段 - 曝气头检测 - 仅需分割不需透视变换
                        from server.utils.image_extractor import extract_from_image
                        
                        air_bubble_coords_file = 'llms/utils/split_coords/Aerobic_pool_front-or_coords.txt'
                        air_bubble_frame_path = str(processed_dir / f"{original_filename}_front_air_bubble{original_ext}")
                        
                        # 分割图像用于曝气头检测 - 根据参数决定是否保存文件
                        air_bubble_image = extract_from_image(
                            image_path=original_frame_path,
                            coordinates=air_bubble_coords_file,
                            save=self.save_intermediate_images,  # 使用类属性
                            output_path=air_bubble_frame_path if self.save_intermediate_images else None,
                            trim_border=False
                        )
                        if self.save_intermediate_images:
                            logging.info(f"已分割好氧池前段图像用于曝气头检测并保存至: {air_bubble_frame_path}")
                        else:
                            logging.info(f"已分割好氧池前段图像用于曝气头检测")
                        
                        # 泡沫面积评估需要透视变换
                        from server.utils.image_extractor_aerobic import transform_image_with_coords
                        
                        bubble_area_coords_file = 'llms/utils/split_coords/Aerobic_pool_front.txt'
                        bubble_area_frame_path = str(processed_dir / f"{original_filename}_front_bubble_area{original_ext}")
                        
                        # 透视变换图像用于泡沫面积评估 - 根据参数决定是否保存文件
                        bubble_area_image, saved_path = transform_image_with_coords(
                            image_path=original_frame_path,
                            coords_file_path=bubble_area_coords_file,
                            save_result=self.save_intermediate_images,  # 使用类属性
                            output_path=bubble_area_frame_path if self.save_intermediate_images else None
                        )
                        if self.save_intermediate_images:
                            logging.info(f"已透视变换好氧池前段图像用于泡沫面积评估并保存至: {bubble_area_frame_path}")
                        else:
                            logging.info(f"已透视变换好氧池前段图像用于泡沫面积评估")
                        
                    else:  # system_prompt_aerobic_single2
                        # 中段 - 曝气头检测 - 仅需分割不需透视变换
                        from server.utils.image_extractor import extract_from_image
                        
                        air_bubble_coords_file = 'llms/utils/split_coords/Aerobic_pool_rear_section-or_coords.txt'
                        air_bubble_frame_path = str(processed_dir / f"{original_filename}_rear_air_bubble{original_ext}")
                        
                        # 分割图像用于曝气头检测 - 根据参数决定是否保存文件
                        air_bubble_image = extract_from_image(
                            image_path=original_frame_path,
                            coordinates=air_bubble_coords_file,
                            save=self.save_intermediate_images,  # 使用类属性
                            output_path=air_bubble_frame_path if self.save_intermediate_images else None,
                            trim_border=False
                        )
                        if self.save_intermediate_images:
                            logging.info(f"已分割好氧池中段图像用于曝气头检测并保存至: {air_bubble_frame_path}")
                        else:
                            logging.info(f"已分割好氧池中段图像用于曝气头检测")
                        
                        # 泡沫面积评估需要透视变换
                        from server.utils.image_extractor_aerobic import transform_image_with_coords
                        
                        bubble_area_coords_file = 'llms/utils/split_coords/Aerobic_pool_rear_section.txt'
                        bubble_area_frame_path = str(processed_dir / f"{original_filename}_rear_bubble_area{original_ext}")
                        
                        # 透视变换图像用于泡沫面积评估 - 根据参数决定是否保存文件
                        bubble_area_image, saved_path = transform_image_with_coords(
                            image_path=original_frame_path,
                            coords_file_path=bubble_area_coords_file,
                            save_result=self.save_intermediate_images,  # 使用类属性
                            output_path=bubble_area_frame_path if self.save_intermediate_images else None
                        )
                        if self.save_intermediate_images:
                            logging.info(f"已透视变换好氧池中段图像用于泡沫面积评估并保存至: {bubble_area_frame_path}")
                        else:
                            logging.info(f"已透视变换好氧池中段图像用于泡沫面积评估")
                    
                    # 处理错误情况
                    if air_bubble_image is None or bubble_area_image is None:
                        logging.error(f"图像处理失败，无法进行分析")
                        return {
                            "当前泡沫覆盖率": "0%",
                            "情况分析": "图像处理失败，无法进行分析",
                            "曝气头是否脱落或者损坏": "否"
                        }
                    
                    # 使用处理后的图像直接调用模型，无论是否保存了中间文件
                    if system_type == 'system_prompt_aerobic_single1':
                        # 第一次调用模型检测曝气头状态 - 直接传递图像数组而非文件路径
                        air_bubble_result = process_image(air_bubble_image, 'system_prompt_aerobic_single1_air_bubble')
                        # 第二次调用模型检测泡沫面积 - 直接传递图像数组而非文件路径
                        bubble_area_result = process_image(bubble_area_image, 'system_prompt_aerobic_single1_bubble_area')
                    else:  # system_prompt_aerobic_single2
                        # 第一次调用模型检测曝气头状态 - 直接传递图像数组而非文件路径
                        air_bubble_result = process_image(air_bubble_image, 'system_prompt_aerobic_single2_air_bubble')
                        # 第二次调用模型检测泡沫面积 - 直接传递图像数组而非文件路径
                        bubble_area_result = process_image(bubble_area_image, 'system_prompt_aerobic_single2_bubble_area')
                    
                    # 合并曝气头和泡沫面积的情况分析
                    air_bubble_analysis = air_bubble_result.get("情况分析", "")
                    bubble_area_analysis = bubble_area_result.get("情况分析", "")
                    
                    # 综合分析结果
                    combined_analysis = ""
                    if air_bubble_analysis and bubble_area_analysis:
                        combined_analysis = f"曝气头状态分析：{air_bubble_analysis}\n\n泡沫面积分析：{bubble_area_analysis}"
                    elif air_bubble_analysis:
                        combined_analysis = f"曝气头状态分析：{air_bubble_analysis}"
                    elif bubble_area_analysis:
                        combined_analysis = f"泡沫面积分析：{bubble_area_analysis}"
                    
                    process_image_result = {
                        "当前泡沫覆盖率": bubble_area_result.get("当前泡沫覆盖率", "0%"),
                        "情况分析": combined_analysis,
                        "曝气头是否脱落或者损坏": air_bubble_result.get("曝气头是否脱落或损坏", "否")
                    }
                    
                    # 记录日志，方便调试
                    logging.info(f"好氧池分析 - 曝气头结果: {air_bubble_result}")
                    logging.info(f"好氧池分析 - 泡沫面积结果: {bubble_area_result}")
                    logging.info(f"好氧池分析 - 合并结果: {process_image_result}")
                    
                    return process_image_result
                else:
                    process_image_result = process_image(str(frame_path), system_type)
                    return process_image_result
        # else: # 没有标准图片使用yolo的识别逻辑
        #     return DeviceDetector().detect_devices(frame)

    def _save_standard_image(self, standard_image_path: str, filename: str) -> Path:
        """保存标准图像
        
        下载并保存标准图像，如果已存在则直接返回路径
        
        Args:
            standard_image_path (str): 标准图像URL
            filename (str): 保存的文件名
            
        Returns:
            Path: 保存后的图像路径
        """
        local_image_path = self.base_dataset_path / 'standard_images' / filename
        if not local_image_path.exists():
            local_image_path.parent.mkdir(parents=True, exist_ok=True)
            response = requests.get(standard_image_path)
            if response.status_code == 200:
                image = Image.open(BytesIO(response.content))
                image.save(str(local_image_path))
                logging.info(f"保存新的标准图片: {local_image_path}")
        return local_image_path



    def _determine_alarm_status(self, coverage_float: float, threshold: float) -> tuple:
        """确定警报状态
        
        Args:
            coverage_float (float): 覆盖率
            threshold (float): 阈值
            
        Returns:
            tuple: (警报状态, 是否异常)
                - 警报状态: 'WARNING' 或 'NO_ALARM'
                - 是否异常: True 或 False
        """
        is_abnormal = coverage_float > threshold
        return ('WARNING' if is_abnormal else 'NO_ALARM', is_abnormal)

    def determine_coverage_level(self, coverage_percentage: float=0) -> str:
        """原始根据泡沫覆盖率确定覆盖等级--》如果没有泡沫面积输入,就根据其他判断
        
        Args:
            coverage_percentage (float): 覆盖率百分比(0-100)
            
        Returns:
            str: 覆盖等级(LOW/MEDIUM/HIGH)
            
        Raises:
            ValueError: 当覆盖率值超出有效范围时
        """
        if not 0 <= coverage_percentage <= 100:
            raise ValueError("覆盖率值超出有效范围 (0-100%)")

        if 0 <= coverage_percentage <= self.coverage_thresholds['low']:
            return self.coverage_level_names['low']
        elif coverage_percentage <= self.coverage_thresholds['medium']:
            return self.coverage_level_names['medium']
        else:
            return self.coverage_level_names['high']
    def alarm_status(self, alarm_status_str: str='') -> str:
        """根据其他状态判断报警信息
        
        Args:
            alarm_status_str (str): 其他状态
            
        Returns:
            str: 报警信息
            
        Raises:

        """
        
        if alarm_status_str == 1:
            return 'HIGH','WARNING'
        else:
            return 'LOW','NO_ALARM'

    def start_test_mode(self):
        """开始测试模式
        
        Returns:
            bool: 是否成功启动测试模式
        """
        self.test_mode = True
        self.current_test_image_index = 0
        return True
        
    def stop_test_mode(self):
        """停止测试模式
        
        Returns:
            bool: 是否成功停止测试模式
        """
        if hasattr(self, 'test_mode'):
            self.test_mode = False
            return True
        return False
