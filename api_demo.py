#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API请求演示脚本
用于请求环境设备接口数据
"""

import requests
import json
from typing import Dict, Any, Optional


def request_device_screen_api() -> Optional[Dict[Any, Any]]:
    """
    请求设备屏幕接口数据
    
    Returns:
        Dict: API响应数据，如果请求失败返回None
    """
    # 接口配置
    base_url = "http://192.168.10.110:888"
    endpoint = "/env-api/device/screen_all"
    
    # 查询参数
    params = {
        "scene": "env_device",
        "query_num": "true", 
        "query_device": "false"
    }
    
    # 认证Token
    token = "NhZTBiMDgwLTBhMzAtNDBkZC05MGQzLTNkYmVkNjdjYzc1ZiJ9.ItNDrvf_d2WH2EF2nxomVw6syjAoFm_3DdHURBhFBuCYg94ZpXGW_pIiRrHOxS0hng-f0SPmOjeHvIMYu7xh2w"
    
    # 请求头设置
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "User-Agent": "Python-API-Client/1.0"
    }
    
    # 构建完整URL
    full_url = f"{base_url}{endpoint}"
    
    try:
        print(f"正在请求接口: {full_url}")
        print(f"查询参数: {params}")
        
        # 发送GET请求
        response = requests.get(
            url=full_url,
            params=params,
            headers=headers,
            timeout=30  # 30秒超时
        )
        
        # 检查HTTP状态码
        if response.status_code == 200:
            print("✅ 请求成功!")
            
            # 解析JSON响应
            try:
                data = response.json()
                print("📄 响应数据:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                return data
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
                return None
                
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return None
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误，请检查网络或服务器地址")
        return None
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return None


def main():
    """主函数"""
    print("🚀 开始执行API请求演示")
    print("=" * 50)
    
    # 执行API请求
    result = request_device_screen_api()
    
    print("=" * 50)
    if result:
        print("✅ 演示完成，数据获取成功")
    else:
        print("❌ 演示完成，数据获取失败")


if __name__ == "__main__":
    main()