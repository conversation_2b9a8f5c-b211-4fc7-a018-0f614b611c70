# 定义数据模型
from datetime import datetime
from pydantic import BaseModel
from typing import List, Optional, Dict, Any, Union


class QueryParams(BaseModel):
    """通用查询参数模型

    Attributes:
        table (str): 要查询的表名
        conditions (dict): 查询条件，键值对形式
        fields (list): 要查询的字段列表
        order_by (str): 排序字段
        limit (int): 返回记录数限制
    """
    table: str
    conditions: dict = None
    fields: list = None
    order_by: str = None
    limit: int = None


class CameraQueryParams(BaseModel):
    """单个摄像头查询参数模型

    Attributes:
        camera_id (str): 摄像头ID
    """
    camera_id: str


class CameraHistoryParams(BaseModel):
    """摄像头历史记录查询参数模型

    Attributes:
        camera_id (str): 摄像头ID
        page_num (int): 页码，默认1
        page_size (int): 每页记录数，默认10
    """
    camera_id: str = None
    page_num: int = 1
    page_size: int = 10
    start_time: datetime = None
    end_time: datetime = None
    camera_ids: list[str] = None


class MultiCameraQueryParams(BaseModel):
    """多摄像头查询参数模型

    Attributes:
        camera_ids (list[str]): 摄像头ID列表
    """
    camera_ids: list[str]


class TimeRangeQueryParams(BaseModel):
    """用于按时间范围查询的参数"""
    camera_id: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    coverage_levels: Optional[List[str]] = None
    page_num: int = 1
    page_size: int = 10


class UpdateReadStatusParams(BaseModel):
    """用于更新摄像头读取状态的参数模型

    Attributes:
        camera_ids (list[str]): 摄像头ID列表
        is_read (int): 读取状态，默认为0（未读）
    """
    camera_ids: list[str]
    is_read: int = 0


class DailyReportQueryParams(BaseModel):
    """日报查询参数模型

    Attributes:
        camera_ids (list[str], optional): 设备ID列表，支持查询多个设备
        start_date (str, optional): 开始日期，格式为'YYYY-MM-DD'
        end_date (str, optional): 结束日期，格式为'YYYY-MM-DD'
        page_num (int): 页码，默认1
        page_size (int): 每页记录数，默认10
        report_name (str, optional): 日报名称，用于模糊查询
    """
    camera_ids: Optional[List[str]] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    page_num: int = 1
    page_size: int = 10
    report_name: Optional[str] = None
