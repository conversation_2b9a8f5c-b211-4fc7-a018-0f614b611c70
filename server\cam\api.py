# 导入必要的库
import os
import sys
from datetime import datetime
from pathlib import Path
from fastapi import APIRouter, BackgroundTasks, HTTPException
from fastapi.responses import FileResponse
from fastapi.concurrency import run_in_threadpool
from server.cam.entity import QueryParams, CameraQueryParams, CameraHistoryParams, MultiCameraQueryParams, TimeRangeQueryParams, UpdateReadStatusParams, DailyReportQueryParams
from server.utils.pg_tool import query_data, connect_db
from server.utils import daily_report_db
from config_file import config
import logging
import tempfile
import markdown
from docx import Document
import pypandoc

# 导入工具函数
from server.utils.doc_utils import convert_with_html2docx

router = APIRouter(prefix='/cam')

IMAGE_ROOT = config.env['environment']['base_paths']['image_root']  # 定义图片根目录路径
IMAGE_OPSHUB_DATASET_ROOT = config.env['environment']['base_paths']['opshub_dataset']  # 定义图片根目录路径
sys.path.append(os.getcwd())  # 添加当前工作目录到系统路径


@router.post("/query")
async def query(params: QueryParams):
    try:
        result = query_data(
            table=params.table,
            conditions=params.conditions,
            fields=params.fields,
            order_by=params.order_by,
            limit=params.limit
        )
        logging.info("query接口请求成功")
        return {
            "msg": "操作成功",
            "code": 200,
            "data": result
        }
        logging.info("query接口请求成功")
    except Exception as e:
        logging.error(f"query接口请求失败: {str(e)}")
        return {
            "msg": f"查询失败: {str(e)}",
            "code": 500,
            "data": None
        }


@router.post("/query_by_camera")
async def query_by_camera(params: CameraQueryParams):
    """
    专门用于通过camera_id查询最新分析结果端点
    返回所有相关字段
    """
    try:
        result = query_data(
            table="frame_analysis_latest",
            conditions={"camera_id": params.camera_id},
            fields=[
                "camera_id", "video_id", "frame_number", "timestamp", "frame_path",
                "coverage_rate", "coverage_level", "alarm_status", "analysis_detail",
                "is_abnormal", "do_value", "mlss_value", "adjustment_suggestion",
                "is_read",
                # 新增加字段failure_reasons_type(数据库增加字段以后生效)
                "failure_reasons_type", "failure_reasons_number",
                "alarmtype"
            ],
            order_by="timestamp DESC",
            limit=1
        )
        if not result:
            return {
                "msg": f"未找到摄像头 {params.camera_id} 的数据",
                "code": 200,
                "data": None
            }

        # 将查询结果转换为带字段名的字典
        fields = [
            "camera_id", "video_id", "frame_number", "timestamp", "frame_path",
            "coverage_rate", "coverage_level", "alarm_status", "analysis_detail",
            "is_abnormal", "do_value", "mlss_value", "adjustment_suggestion",
            "is_read",
            # 新增加字段failure_reasons_type(数据库增加字段以后生效)
            "failure_reasons_type", "failure_reasons_number",
            "alarmtype"
        ]
        result_dict = dict(zip(fields, result[0]))
        # 将alarmtype字段重命名为alarmType
        if "alarmtype" in result_dict:
            result_dict["alarmType"] = result_dict.pop("alarmtype")
        logging.info("query_by_camera接口请求成功")
        return {
            "msg": "成功",
            "code": 200,
            "data": result_dict
        }

    except Exception as e:
        logging.error(f"query_by_camera接口请求失败: {str(e)}")
        return {
            "msg": f"查询失败: {str(e)}",
            "code": 500,
            "data": None
        }


@router.post("/query_camera_history")
async def query_camera_history(params: CameraHistoryParams):
    """
    查询指定摄像头的历史数据，只返回覆盖等级为 MEDIUM 和 HIGH 的记录
    支持分页查询，按时间戳降序排序
    """
    try:
        conn = connect_db()
        if not conn:
            return {
                "msg": "数据库连接失败",
                "code": 500,
                "data": None
            }

        try:
            cur = conn.cursor()

            # 计算偏移量
            offset = (params.page_num - 1) * params.page_size

            # 构建查询 SQL # "failure_reasons_type" # 新增加字段failure_reasons_type(数据库增加字段以后生效)
            query_sql = """
                SELECT 
                    camera_id, video_id, frame_number, timestamp, frame_path,
                    coverage_rate, coverage_level, alarm_status, analysis_detail,
                    is_abnormal, do_value, mlss_value, adjustment_suggestion, is_read, id,
                    failure_reasons_type , failure_reasons_number, alarmtype
                FROM frame_analysis 
                WHERE  coverage_level IN ('MEDIUM', 'HIGH') 
                
            """
            if params.start_time and params.end_time:
                query_sql += f"AND timestamp between '{params.start_time}' and '{params.end_time}'"
            if params.camera_id:
                query_sql += f"AND camera_id = '{params.camera_id}'"
            if params.camera_ids:
                query_sql += f"AND camera_id in {tuple(params.camera_ids)}"
            query_sql += " ORDER BY timestamp DESC"
            query_sql += f" LIMIT {params.page_size} OFFSET {offset}"
            # 执行分页查询
            cur.execute(query_sql)
            results = cur.fetchall()
            print(results)

            # 获取总记录数
            count_sql = """
                SELECT COUNT(*) 
                FROM frame_analysis 
                WHERE camera_id = %s 
                AND coverage_level IN ('MEDIUM', 'HIGH')
            """
            cur.execute(count_sql, (params.camera_id,))
            total_count = cur.fetchone()[0]

            # 将查询结果转换为字典列表 # "failure_reasons_type" # 新增加字段failure_reasons_type(数据库增加字段以后生效)
            fields = [
                "camera_id", "video_id", "frame_number", "timestamp", "frame_path",
                "coverage_rate", "coverage_level", "alarm_status", "analysis_detail",
                "is_abnormal", "do_value", "mlss_value", "adjustment_suggestion", "is_read", "id",
                # 新增加字段failure_reasons_type(数据库增加字段以后生效)
                "failure_reasons_type", "failure_reasons_number",
                "alarmtype"
            ]

            # 构建返回数据格式
            result_list = []
            for row in results:
                result_dict = dict(zip(fields, row))
                # 将alarmtype字段重命名为alarmType
                if "alarmtype" in result_dict:
                    result_dict["alarmType"] = result_dict.pop("alarmtype")

                analysis_detail = row[8]  # analysis_detail字段的索引是8

                """
                 "倾斜检测": tilt_detection_count,
                            "垃圾占比高": high_garbage_ratio_count,
                            "大件垃圾": large_garbage_count,
                            # "栅条损坏": damaged_grid_count （栅条损坏去除检测分析了）
                
                """

                title = ""
                # 检查大件垃圾
                if analysis_detail and "是否有大件垃圾: 有" in analysis_detail:
                    title = "大件垃圾"
                # 检查栅条异常
                if analysis_detail and "栅条监是否异常: 有" in analysis_detail:
                    title += "、栅条损坏"

                # 检查垃圾占比高（假设超过20%就算高）
                if analysis_detail and "垃圾占比" in analysis_detail:
                    try:
                        ratio_text = analysis_detail.split(
                            "垃圾占比:")[1].split("%")[0].strip()
                        ratio = float(ratio_text)
                        if ratio > 20:
                            title += "、垃圾占比高"
                    except (IndexError, ValueError):
                        pass
                result_dict['title'] = title

                # 确保 timestamp 是字符串格式
                if isinstance(result_dict['timestamp'], datetime):
                    result_dict['timestamp'] = result_dict['timestamp'].isoformat()
                result_list.append(result_dict)
            logging.info("query_camera_history接口请求成功")
            return {
                "code": 200,
                "msg": "成功",
                "data": {
                    "rows": result_list,
                    "total": total_count,
                    "page_num": params.page_num,
                    "page_size": params.page_size,
                    "total_pages": (total_count + params.page_size - 1) // params.page_size
                }
            }

        except Exception as e:
            logging.error(f"query_camera_history接口请求失败: {str(e)}")
            return {
                "msg": f"查询失败: {str(e)}",
                "code": 500,
                "data": None
            }
        finally:
            if cur:
                cur.close()
            if conn:
                conn.close()

    except Exception as e:
        return {
            "msg": f"系统错误: {str(e)}",
            "code": 500,
            "data": None
        }



@router.post("/query_camera_real_data")
async def query_camera_real_data(params: CameraHistoryParams):
    """
    查询指定摄像头的历史数据，只返回覆盖等级为 MEDIUM 和 HIGH 的记录
    支持分页查询，按时间戳降序排序
    """
    try:
        conn = connect_db()
        if not conn:
            return {
                "msg": "数据库连接失败",
                "code": 500,
                "data": None
            }

        try:
            cur = conn.cursor()

            # 计算偏移量
            offset = (params.page_num - 1) * params.page_size

            # 构建查询 SQL # "failure_reasons_type" # 新增加字段failure_reasons_type(数据库增加字段以后生效)
            query_sql = """
                SELECT
                    a.camera_id,
                    a.video_id,
                    a.frame_number,
                    a.TIMESTAMP,
                    a.frame_path,
                    a.coverage_rate,
                    a.coverage_level,
                    a.alarm_status,
                    a.analysis_detail,
                    a.is_abnormal,
                    a.do_value,
                    a.mlss_value,
                    a.adjustment_suggestion,
                    a.is_read,
                    b.ID,
                    a.failure_reasons_type,
                    a.failure_reasons_number,
                    a.is_opshub_read
                FROM
                    frame_analysis_latest
                    A LEFT JOIN ( SELECT MAX ( ID ) AS ID, camera_id FROM frame_analysis GROUP BY camera_id ) b ON A.camera_id  = b.camera_id
                WHERE
                    coverage_level IN ( 'MEDIUM', 'HIGH' ) 
                    AND failure_reasons_number > 0
                
            """
            if params.start_time and params.end_time:
                query_sql += f"AND a.timestamp between '{params.start_time}' and '{params.end_time}'"
            if params.camera_id:
                query_sql += f"AND a.camera_id = '{params.camera_id}'"
            if params.camera_ids:
                query_sql += f"AND a.camera_id in {tuple(params.camera_ids)}"
            query_sql += " ORDER BY a.timestamp DESC"
            query_sql += f" LIMIT {params.page_size} OFFSET {offset}"
            # 执行分页查询
            cur.execute(query_sql)
            results = cur.fetchall()

            # 获取总记录数
            count_sql = """
                SELECT COUNT(*) 
                FROM frame_analysis_latest
                WHERE camera_id = %s 
                AND coverage_level IN ('MEDIUM', 'HIGH') 
                AND failure_reasons_number > 0
            """
            cur.execute(count_sql, (params.camera_id,))
            total_count = cur.fetchone()[0]

            # 将查询结果转换为字典列表 # "failure_reasons_type" # 新增加字段failure_reasons_type(数据库增加字段以后生效)
            fields = [
                "camera_id", "video_id", "frame_number", "timestamp", "frame_path",
                "coverage_rate", "coverage_level", "alarm_status", "analysis_detail",
                "is_abnormal", "do_value", "mlss_value", "adjustment_suggestion", "is_read", "id",
                # 新增加字段failure_reasons_type(数据库增加字段以后生效)
                "failure_reasons_type", "failure_reasons_number", "is_opshub_read"
            ]

            # 构建返回数据格式
            result_list = []
            for row in results:
                result_dict = dict(zip(fields, row))
                # 将alarmtype字段重命名为alarmType
                if "alarmtype" in result_dict:
                    result_dict["alarmType"] = result_dict.pop("alarmtype")

                analysis_detail = row[8]  # analysis_detail字段的索引是8

                """
                 "倾斜检测": tilt_detection_count,
                            "垃圾占比高": high_garbage_ratio_count,
                            "大件垃圾": large_garbage_count,
                            # "栅条损坏": damaged_grid_count （栅条损坏去除检测分析了）
                
                """
                failure_reasons_type = row[15]  # failure_reasons_type字段的索引是16
                if isinstance(failure_reasons_type, list) and len(failure_reasons_type) > 0:
                    result_dict['title'] = "、".join(failure_reasons_type)
                # title = ""
                # # 检查大件垃圾
                # if analysis_detail and "是否有大件垃圾: 有" in analysis_detail:
                #     title = "大件垃圾"
                # # 检查栅条异常
                # if analysis_detail and "栅条监是否异常: 有" in analysis_detail:
                #     title += "、栅条损坏"

                # # 检查垃圾占比高（假设超过20%就算高）
                # if analysis_detail and "垃圾占比" in analysis_detail:
                #     try:
                #         ratio_text = analysis_detail.split(
                #             "垃圾占比:")[1].split("%")[0].strip()
                #         ratio = float(ratio_text)
                #         if ratio > 20:
                #             title += "、垃圾占比高"
                #     except (IndexError, ValueError):
                #         pass
                # result_dict['title'] = title

                # 确保 timestamp 是字符串格式
                if isinstance(result_dict['timestamp'], datetime):
                    result_dict['timestamp'] = result_dict['timestamp'].isoformat()
                result_list.append(result_dict)
            logging.info("query_camera_history接口请求成功")
            return {
                "code": 200,
                "msg": "成功",
                "data": {
                    "rows": result_list,
                    "total": total_count,
                    "page_num": params.page_num,
                    "page_size": params.page_size,
                    "total_pages": (total_count + params.page_size - 1) // params.page_size
                }
            }

        except Exception as e:
            logging.error(f"query_camera_history接口请求失败: {str(e)}")
            return {
                "msg": f"查询失败: {str(e)}",
                "code": 500,
                "data": None
            }
        finally:
            if cur:
                cur.close()
            if conn:
                conn.close()

    except Exception as e:
        return {
            "msg": f"系统错误: {str(e)}",
            "code": 500,
            "data": None
        }


@router.post("/query_by_cameras")
async def query_by_cameras(params: MultiCameraQueryParams):
    """
    用于同时查询多个camera_id的最新分析结果
    返回一个包含所有camera_id结果的数组，并附带统计信息
    """
    try:
        rows = []
        for camera_id in params.camera_ids:
            result = query_data(
                table="frame_analysis_latest",
                conditions={"camera_id": camera_id},
                fields=[
                    "camera_id", "video_id", "frame_number", "timestamp", "frame_path",
                    "coverage_rate", "coverage_level", "alarm_status", "analysis_detail",
                    "is_abnormal", "do_value", "mlss_value", "adjustment_suggestion",
                    "is_read",
                    # 新增加字段failure_reasons_type(数据库增加字段以后生效)
                    "failure_reasons_type", "failure_reasons_number",
                    "alarmtype"
                ],
                order_by="timestamp DESC",
                limit=1
            )

            if result:
                # 将查询结果转换为带字段名的字典
                fields = [
                    "camera_id", "video_id", "frame_number", "timestamp", "frame_path",
                    "coverage_rate", "coverage_level", "alarm_status", "analysis_detail",
                    "is_abnormal", "do_value", "mlss_value", "adjustment_suggestion",
                    "is_read",
                    # 新增加字段failure_reasons_type(数据库增加字段以后生效)
                    "failure_reasons_type", "failure_reasons_number",
                    "alarmtype"
                ]
                result_dict = dict(zip(fields, result[0]))
                # 将alarmtype字段重命名为alarmType
                if "alarmtype" in result_dict:
                    result_dict["alarmType"] = result_dict.pop("alarmtype")
                rows.append(result_dict)

        # 计算统计信息
        total = len(params.camera_ids)
        success_count = len(rows)
        failed_count = total - success_count
        logging.info("query_by_cameras接口请求成功")
        return {
            "code": 200,
            "msg": "成功",
            "data": {
                "rows": rows,
                "total": total,
                "success_count": success_count,
                "failed_count": failed_count
            }
        }
    except Exception as e:
        logging.error(f"query_by_cameras接口请求失败: {str(e)}")
        return {
            "code": 500,
            "msg": f"查询失败: {str(e)}",
            "data": None
        }


@router.get("/get-image/{path:path}")
async def get_image(path: str):
    """
    返回本地图片给前端。
    参数:
        path: str - 图片的相对路径，例如: '2024/11/21/frame_4010_2024_11_21_18_21_24.jpg'
    """
    # 使用 Path 来安全地处理路径拼接
    image_path = Path(IMAGE_ROOT) / path

    # 验证路径是否在允许的根目录下，防止目录遍历攻击
    try:
        image_path = image_path.resolve()
        root_path = Path(IMAGE_ROOT).resolve()
        if not str(image_path).startswith(str(root_path)):
            return {"error": "访问路径不允许"}
    except Exception as e:
        logging.error(f"get-image接口请求失败: {str(e)}")
        return {"error": "无效的路径"}

    if image_path.is_file():
        logging.info("图片请求成功")
        return FileResponse(str(image_path), media_type="image/jpeg", filename=path.split('/')[-1])
    else:
        logging.error(f"图片 {path} 不存在")
        return {"error": f"图片 {path} 不存在"}


@router.post("/update_read_status/{id:path}")
async def update_read_status(id: int):
    """
    更新指定ID的记录为已读状态
    """

    try:
        conn = connect_db()
        if not conn:
            return {
                "msg": "数据库连接失败",
                "code": 500,
                "data": None
            }

        cur = conn.cursor()

        # 构建更新SQL

        select_sql = "SELECT camera_id, is_read FROM frame_analysis WHERE id = %s"
        cur.execute(select_sql, (id,))
        result = cur.fetchone()
        print(result)
        if result:
            camera_id = result[0]
            is_read = result[1]
            if is_read == 0:
                update_sql = "UPDATE frame_analysis SET is_read = '1' WHERE id = %s"
                cur.execute(update_sql, (id,))
            conn.commit()

            update_sql = "UPDATE frame_analysis_latest SET is_opshub_read = '1' WHERE camera_id = %s"
            cur.execute(update_sql, (camera_id,))
            conn.commit()
        logging.info("update_read_status接口请求成功")
        return {
            "msg": "更新成功",
            "code": 200,
            "data": None
        }
    except Exception as e:
        logging.error(f"update_read_status接口请求失败: {str(e)}")
        return {
            "msg": f"更新失败: {str(e)}",
            "code": 500,
            "data": None
        }
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


@router.post("/query_by_time_range")
async def query_by_time_range(params: TimeRangeQueryParams):
    """
    按照camera_id、开始时间和结束时间查询数据
    支持分页查询，按时间戳降序排序
    failure_reasons_type字段包含了检测到的故障类型
    返回四个统计参数：倾斜检测、垃圾占比高、大件垃圾、栅条损坏以及对应的数量（栅条损坏的不进行检测了）

    故障类型:fault_types = ["耙斗倾斜", "耙斗卡住", "垃圾占比过高", "大件垃圾", 
                                    "反冲洗不均匀", "曝气头可能出现脱落或损坏", 
                                    "泡沫面积增长", "出现青苔", "树叶堆积过多", 
                                    "撇渣管浮渣堆积", "排水堰出现水流变化","二沉池水面异常","排渣堰门浮渣堆积"]
    """
    try:
        conn = connect_db()
        if not conn:
            return {
                "msg": "数据库连接失败",
                "code": 500,
                "data": None
            }

        try:
            cur = conn.cursor()

            # 计算偏移量
            offset = (params.page_num - 1) * params.page_size

            # 构建查询 SQL
            # 20250410 改为实时
            query_sql = """
                SELECT 
                    camera_id, video_id, frame_number, timestamp, frame_path,
                    coverage_rate, coverage_level, alarm_status, analysis_detail,adjustment_suggestion,
                    is_abnormal, do_value, mlss_value, is_read,
                    failure_reasons_type
                FROM frame_analysis_latest
                WHERE 1=1 and failure_reasons_number > 0 
            """

            # 添加查询条件
            query_params = []

            if params.camera_id:
                query_sql += " AND camera_id = %s"
                query_params.append(params.camera_id)

            if params.start_time:
                query_sql += " AND timestamp >= %s"
                query_params.append(params.start_time)

            if params.end_time:
                query_sql += " AND timestamp <= %s"
                query_params.append(params.end_time)

            if params.coverage_levels:
                placeholders = ', '.join(['%s'] * len(params.coverage_levels))
                query_sql += f" AND coverage_level IN ({placeholders})"
                query_params.extend(params.coverage_levels)

            # 添加排序和分页
            query_sql += " ORDER BY timestamp DESC"
            query_sql += " LIMIT %s OFFSET %s"
            query_params.extend([params.page_size, offset])

            # 执行分页查询
            cur.execute(query_sql, query_params)
            results = cur.fetchall()

            # 获取总记录数
            count_sql = """
                SELECT COUNT(*) 
                FROM frame_analysis_latest 
                WHERE 1=1
            """

            count_params = []

            if params.camera_id:
                count_sql += " AND camera_id = %s"
                count_params.append(params.camera_id)

            if params.start_time:
                count_sql += " AND timestamp >= %s"
                count_params.append(params.start_time)

            if params.end_time:
                count_sql += " AND timestamp <= %s"
                count_params.append(params.end_time)

            if params.coverage_levels:
                placeholders = ', '.join(['%s'] * len(params.coverage_levels))
                count_sql += f" AND coverage_level IN ({placeholders})"
                count_params.extend(params.coverage_levels)

            cur.execute(count_sql, count_params)
            total_count = cur.fetchone()[0]

            # 通过failure_reasons_type字段进行故障类型统计
            tilt_detection_count = 0  # 倾斜检测
            high_garbage_ratio_count = 0  # 垃圾占比高
            large_garbage_count = 0  # 大件垃圾
            backwash_uneven_count = 0  # 反冲洗不均匀
            aerator_damage_count = 0  # 曝气头可能出现脱落或损坏
            foam_increase_count = 0  # 泡沫面积增长
            moss_appearance_count = 0  # 出现青苔
            rake_stuck_count = 0  # 耙斗卡住
            overall_color_deepening_count = 0 # 水面整体颜色加深或变浅
            leaf_accumulation_count = 0  # 树叶堆积过多
            slag_accumulation_count = 0  # 撇渣管浮渣堆积
            water_flow_change_count = 0  # 排水堰出现水流变化
            secondary_pool_abnormal_count = 0  # 二沉池水面异常
            slag_weir_gate_accumulation_count = 0  # 排渣堰门浮渣堆积
            # ********* 原始从analysis_detail字段提取故障的类型 ********>>> #
            # 分析每条记录的analysis_detail字段
            # for row in results:
            #     analysis_detail = row[8]  # analysis_detail字段的索引是8

            # # 检查大件垃圾
            # if analysis_detail and "是否有大件垃圾: 有" in analysis_detail:
            #     large_garbage_count += 1

            # # 检查栅条异常
            # if analysis_detail and "栅条监是否异常: 有" in analysis_detail:
            #     damaged_grid_count += 1

            # # 检查垃圾占比高（假设超过20%就算高）
            # if analysis_detail and "垃圾占比" in analysis_detail:
            #     try:
            #         ratio_text = analysis_detail.split(
            #             "垃圾占比:")[1].split("%")[0].strip()
            #         ratio = float(ratio_text)
            #         if ratio > 20:
            #             high_garbage_ratio_count += 1
            #     except (IndexError, ValueError):
            #         pass
            # 倾斜检测暂时为0，因为示例中没有相关信息
            # 更改为使用failure_reasons_type字段进行判断
            # ********* 原始从analysis_detail字段提取故障的类型 ********<<< #
            for row in results:
                failure_reasons_type = row[14]  # failure_reasons_type字段的索引是15 这里应该是14或者13吧(如果不加adjustment_suggestion)

                if failure_reasons_type:
                    try:
                        # 处理列表格式
                        if isinstance(failure_reasons_type, list):
                            if "耙斗倾斜" in failure_reasons_type:
                                tilt_detection_count += 1
                            if "耙斗卡住" in failure_reasons_type:
                                rake_stuck_count += 1
                            if "垃圾占比过高" in failure_reasons_type:
                                high_garbage_ratio_count += 1
                            if "大件垃圾" in failure_reasons_type:
                                large_garbage_count += 1
                            if "反冲洗不均匀" in failure_reasons_type:
                                backwash_uneven_count += 1
                            if "曝气头可能出现脱落或损坏" in failure_reasons_type:
                                aerator_damage_count += 1
                            if "泡沫面积增长" in failure_reasons_type:
                                foam_increase_count += 1
                            if "出现青苔" in failure_reasons_type:
                                moss_appearance_count += 1
                            if "水面整体颜色加深或变浅" in failure_reasons_type:
                                overall_color_deepening_count += 1
                            if "树叶堆积过多" in failure_reasons_type:
                                leaf_accumulation_count += 1
                            if "撇渣管浮渣堆积" in failure_reasons_type:
                                slag_accumulation_count += 1
                            if "排水堰出现水流变化" in failure_reasons_type:
                                water_flow_change_count += 1
                            if "二沉池水面异常" in failure_reasons_type:
                                secondary_pool_abnormal_count += 1
                            if "排渣堰门浮渣堆积" in failure_reasons_type:
                                slag_weir_gate_accumulation_count += 1

                        # 处理字典格式(增加对字典格式的处理逻辑)
                        elif isinstance(failure_reasons_type, dict):
                            keys = failure_reasons_type.keys()
                            if "耙斗倾斜" in keys:
                                tilt_detection_count += 1
                            if "耙斗卡住" in keys:
                                rake_stuck_count += 1
                            if "垃圾占比过高" in keys:
                                high_garbage_ratio_count += 1
                            if "大件垃圾" in keys:
                                large_garbage_count += 1
                            if "反冲洗不均匀" in keys:
                                backwash_uneven_count += 1
                            if "曝气头可能出现脱落或损坏" in keys:
                                aerator_damage_count += 1
                            if "泡沫面积增长" in keys:
                                foam_increase_count += 1
                            if "出现青苔" in keys:
                                moss_appearance_count += 1
                            if "水面整体颜色加深或变浅" in keys:
                                overall_color_deepening_count += 1
                            if "树叶堆积过多" in keys:
                                leaf_accumulation_count += 1
                            if "撇渣管浮渣堆积" in keys:
                                slag_accumulation_count += 1
                            if "排水堰出现水流变化" in keys:
                                water_flow_change_count += 1
                            if "二沉池水面异常" in keys:
                                secondary_pool_abnormal_count += 1
                            if "排渣堰门浮渣堆积" in keys:
                                slag_weir_gate_accumulation_count += 1
                    except Exception as e:
                        logging.warning(f"处理failure_reasons_type时出错: {str(e)}")

            # 构建返回数据格式
            logging.info("query_by_time_range接口请求成功")
            # 更新风险等级计算公式，包含所有故障类型
            risk_levels = tilt_detection_count * 2 + rake_stuck_count * 2 + \
                large_garbage_count * 3 + high_garbage_ratio_count * 4 + \
                backwash_uneven_count * 2 + \
                aerator_damage_count * 3 + foam_increase_count * 1 + \
                moss_appearance_count * 1 + overall_color_deepening_count * 1 + \
                leaf_accumulation_count * 2 + slag_accumulation_count * 2 + \
                water_flow_change_count * 3 + secondary_pool_abnormal_count * 2 + \
                slag_weir_gate_accumulation_count * 2  # 新增故障类型权重
            risk_level_name = ""
            # A 0-5
            # B 5-10
            # C 10-15
            # D 15-20
            # E > 20
            if risk_levels >= 20:
                risk_level_name = "E"
            elif risk_levels >= 15:
                risk_level_name = "D"
            elif risk_levels >= 10:
                risk_level_name = "C"
            elif risk_levels >= 5:
                risk_level_name = "B"
            else:
                risk_level_name = "A"
            return {
                "code": 200,
                "msg": "成功",
                "data": {
                    "rows": [
                        {
                            "耙斗倾斜": tilt_detection_count,
                            "垃圾占比过高": high_garbage_ratio_count,
                            "大件垃圾": large_garbage_count,
                            "反冲洗不均匀": backwash_uneven_count,
                            "曝气头可能出现脱落或损坏": aerator_damage_count,
                            "泡沫面积增长": foam_increase_count,
                            "出现青苔": moss_appearance_count,
                            "耙斗卡住": rake_stuck_count,
                            "水面整体颜色加深或变浅": overall_color_deepening_count,
                            "树叶堆积过多": leaf_accumulation_count,
                            "撇渣管浮渣堆积": slag_accumulation_count,
                            "排水堰出现水流变化": water_flow_change_count,
                            "二沉池水面异常": secondary_pool_abnormal_count,
                            "排渣堰门浮渣堆积": slag_weir_gate_accumulation_count
                        }
                    ],
                    "risk_levels": risk_levels,
                    "risk_level_name": risk_level_name,
                    "total": total_count,
                    "page_num": params.page_num,
                    "page_size": params.page_size,
                    "total_pages": (total_count + params.page_size - 1) // params.page_size
                }
            }

        except Exception as e:
            logging.error(f"query_by_time_range接口请求失败: {str(e)}")
            return {
                "msg": f"查询失败: {str(e)}",
                "code": 500,
                "data": None
            }
        finally:
            if cur:
                cur.close()
            if conn:
                conn.close()

    except Exception as e:
        logging.error(f"query_by_time_range接口系统错误: {str(e)}")
        return {
            "msg": f"系统错误: {str(e)}",
            "code": 500,
            "data": None
        }


@router.post("/query_camera_history_with_run_day")
async def query_camera_history_with_run_day(params: CameraHistoryParams):
    """
    风控总览-安全风险提示，无风险运行多少天
    """
    try:
        conn = connect_db()
        if not conn:
            return {
                "msg": "数据库连接失败",
                "code": 500,
                "data": None
            }

        try:
            cur = conn.cursor()

            # 计算偏移量
            start_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999)

            # 构建查询 SQL # "failure_reasons_type" # 新增加字段failure_reasons_type(数据库增加字段以后生效)
            query_sql = f"""
                SELECT 
                    failure_reasons_type, failure_reasons_number
                FROM frame_analysis 
                WHERE  timestamp between '{start_time}' and '{end_time}' and failure_reasons_number > 0
            """
            # 执行分页查询
            cur.execute(query_sql)
            results = cur.fetchall()
            tilt_detection_count = 0  # 倾斜检测
            high_garbage_ratio_count = 0  # 垃圾占比高
            large_garbage_count = 0  # 大件垃圾
            backwash_uneven_count = 0  # 反冲洗不均匀
            aerator_damage_count = 0  # 曝气头可能出现脱落或损坏
            foam_increase_count = 0  # 泡沫面积增长
            moss_appearance_count = 0  # 出现青苔
            rake_stuck_count = 0  # 耙斗卡住
            overall_color_deepening_count = 0 # 整体颜色加深或变浅
            leaf_accumulation_count = 0  # 树叶堆积过多
            slag_accumulation_count = 0  # 撇渣管浮渣堆积
            water_flow_change_count = 0  # 排水堰出现水流变化
            secondary_pool_abnormal_count = 0  # 二沉池水面异常
            slag_weir_gate_accumulation_count = 0  # 排渣堰门浮渣堆积
            for row in results:
                failure_reasons_type = row[0]
                failure_reasons_number = row[1]
                if failure_reasons_type:
                    try:
                        # 处理列表格式
                        if isinstance(failure_reasons_type, list):
                            if "耙斗倾斜" in failure_reasons_type:
                                tilt_detection_count += 1
                            if "耙斗卡住" in failure_reasons_type:
                                rake_stuck_count += 1
                            if "垃圾占比过高" in failure_reasons_type:
                                high_garbage_ratio_count += 1
                            if "大件垃圾" in failure_reasons_type:
                                large_garbage_count += 1
                            if "反冲洗不均匀" in failure_reasons_type:
                                backwash_uneven_count += 1
                            if "曝气头可能出现脱落或损坏" in failure_reasons_type:
                                aerator_damage_count += 1
                            if "泡沫面积增长" in failure_reasons_type:
                                foam_increase_count += 1
                            if "出现青苔" in failure_reasons_type:
                                moss_appearance_count += 1
                            if "水面整体颜色加深或变浅" in failure_reasons_type:
                                overall_color_deepening_count += 1
                            if "树叶堆积过多" in failure_reasons_type:
                                leaf_accumulation_count += 1
                            if "撇渣管浮渣堆积" in failure_reasons_type:
                                slag_accumulation_count += 1
                            if "排水堰出现水流变化" in failure_reasons_type:
                                water_flow_change_count += 1
                            if "二沉池水面异常" in failure_reasons_type:
                                secondary_pool_abnormal_count += 1
                            if "排渣堰门浮渣堆积" in failure_reasons_type:
                                slag_weir_gate_accumulation_count += 1
                    except Exception as e:
                        logging.warning(f"处理failure_reasons_type时出错: {str(e)}")

            risk_levels = tilt_detection_count * 2 + rake_stuck_count * 2 + \
                large_garbage_count * 3 + high_garbage_ratio_count * 4 + \
                backwash_uneven_count * 2 + \
                aerator_damage_count * 3 + foam_increase_count * 1 + \
                moss_appearance_count * 1 + overall_color_deepening_count * 1 + \
                leaf_accumulation_count * 2 + slag_accumulation_count * 2 + \
                water_flow_change_count * 3 + secondary_pool_abnormal_count * 2 + \
                slag_weir_gate_accumulation_count * 2  # 新增故障类型权重

            if risk_levels > 0:
                risk_level_name = "有风险"
                # 查找最近一个无风险
                sql = f"""
                    SELECT 
                        timestamp
                    FROM frame_analysis 
                    WHERE  timestamp < '{start_time}' and failure_reasons_number > 0
                    ORDER BY timestamp DESC
                    LIMIT 1
                """
            else:
                risk_level_name = "无风险"
                sql = f"""
                    SELECT 
                        timestamp
                    FROM frame_analysis 
                    WHERE  timestamp < '{start_time}' and failure_reasons_number = 0
                    ORDER BY timestamp DESC
                    LIMIT 1
                """
                # 查找最近一个有风险
            cur.execute(sql)
            result = cur.fetchone()
            print(result)
            run_days = 1
            if result:
                last_risk_time = result[0]
                run_days = (start_time - last_risk_time).days + 1
            else:
                last_risk_time = None

            # A 0-5
            # B 5-10
            # C 10-15
            # D 15-20
            # E > 20
            if risk_levels >= 20:
                risk_level_name = "E"
            elif risk_levels >= 15:
                risk_level_name = "D"
            elif risk_levels >= 10:
                risk_level_name = "C"
            elif risk_levels >= 5:
                risk_level_name = "B"
            else:
                risk_level_name = "A"

            return {
                "code": 200,
                "msg": "成功",
                "data": {
                    "run_days": run_days,
                    "risk_level_name": risk_level_name,
                    "risk_levels": risk_levels
                }
            }

        except Exception as e:
            logging.error(f"query_camera_history接口请求失败: {str(e)}")
            return {
                "msg": f"查询失败: {str(e)}",
                "code": 500,
                "data": None
            }
        finally:
            if cur:
                cur.close()
            if conn:
                conn.close()

    except Exception as e:
        return {
            "msg": f"系统错误: {str(e)}",
            "code": 500,
            "data": None
        }

@router.get("/get_cam_last_image/{camera_id:path}")
async def get_cam_last_image(camera_id: str):
    """
    返回本地图片给前端。
    参数:
        path: str - 图片的相对路径，例如: '2024/11/21/frame_4010_2024_11_21_18_21_24.jpg'
    """
    # 使用 Path 来安全地处理路径拼接
    image_path = Path(IMAGE_OPSHUB_DATASET_ROOT) / camera_id
    print(image_path)
    # 验证路径是否在允许的根目录下，防止目录遍历攻击
    try:
        image_path = image_path.resolve()
        root_path = Path(IMAGE_OPSHUB_DATASET_ROOT).resolve()
        
        if not str(image_path).startswith(str(root_path)):
            return {"error": "访问路径不允许"}
    except Exception as e:
        logging.error(f"get-image接口请求失败: {str(e)}")
        return {"error": "无效的路径"}
    photo_files = list(image_path.glob("*.jpg"))
    print(photo_files, "-----------------")
    if not photo_files:
        return None

    # 使用文件的修改时间进行排序，最新的在前面
    latest_photo = max(photo_files, key=lambda f: f.stat().st_mtime)

    if latest_photo.is_file():
        logging.info("图片请求成功")
        print(latest_photo.as_posix())
        return FileResponse(str(latest_photo.as_posix()), media_type="image/jpeg", filename=camera_id + ".jpg")
    else:
        logging.error(f"图片 {camera_id} 不存在")
        return {"error": f"图片 {camera_id} 不存在"}


@router.post("/update_latest_read_status")
async def update_latest_read_status(params: UpdateReadStatusParams):
    """
    更新frame_analysis_latest表中多个camera_id记录的is_read状态
    
    参数:
        params: 包含camera_ids列表和is_read值的参数对象
    
    返回:
        成功: {"msg": "更新成功", "code": 200, "data": {"total": x, "updated": y}}
        失败: {"msg": "更新失败: {错误信息}", "code": 500, "data": None}
    """
    try:
        conn = connect_db()
        if not conn:
            return {
                "msg": "数据库连接失败",
                "code": 500,
                "data": None
            }

        try:
            cur = conn.cursor()
            
            if not params.camera_ids:
                return {
                    "msg": "摄像头ID列表不能为空",
                    "code": 400,
                    "data": None
                }
            
            total_count = len(params.camera_ids)
            updated_count = 0
            
            # 使用 IN 操作符一次性更新多个 camera_id
            placeholders = ', '.join(['%s'] * len(params.camera_ids))
            update_sql = f"UPDATE frame_analysis_latest SET is_read = %s WHERE camera_id IN ({placeholders})"
            
            # 准备参数：第一个是 is_read 值，后面是所有 camera_ids
            params_values = [params.is_read] + params.camera_ids
            
            cur.execute(update_sql, params_values)
            conn.commit()
            
            # 获取更新的行数
            updated_count = cur.rowcount
            
            logging.info(f"成功更新 {updated_count}/{total_count} 个摄像头的is_read状态为 {params.is_read}")
            return {
                "msg": "更新成功",
                "code": 200,
                "data": {
                    "total": total_count,
                    "updated": updated_count
                }
            }
            
        except Exception as e:
            logging.error(f"update_latest_read_status接口请求失败: {str(e)}")
            return {
                "msg": f"更新失败: {str(e)}",
                "code": 500,
                "data": None
            }
        finally:
            if cur:
                cur.close()
            if conn:
                conn.close()
                
    except Exception as e:
        logging.error(f"update_latest_read_status系统错误: {str(e)}")
        return {
            "msg": f"系统错误: {str(e)}",
            "code": 500,
            "data": None
        }


@router.post("/query_daily_reports")
async def query_daily_reports(params: DailyReportQueryParams):
    """
    查询设备日报数据
    支持单个或多个设备查询，支持时间范围筛选和分页
    
    Args:
        params (DailyReportQueryParams): 查询参数
            - camera_ids: 设备ID列表，可选，不传则查询所有设备
            - start_date: 开始日期，格式'YYYY-MM-DD'，可选，不传则默认查询最近3天
            - end_date: 结束日期，格式'YYYY-MM-DD'，可选，不传则默认为当前日期
            - page_num: 页码，默认1
            - page_size: 每页记录数，默认10
            - report_name: 日报名称，可选，用于模糊查询
    
    Returns:
        dict: 包含日报数据列表、总数、分页信息的响应
    """
    # 导入datetime模块
    from datetime import datetime, timedelta
    
    try:
        conn = connect_db()
        if not conn:
            return {
                "msg": "数据库连接失败",
                "code": 500,
                "data": None
            }
        
        try:
            cur = conn.cursor()
            
            # 设置默认时间范围（如果没有提供）
            if not params.start_date and not params.end_date:
                # 默认查询最近3天
                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
            else:
                start_date = params.start_date
                end_date = params.end_date or datetime.now().strftime('%Y-%m-%d')
            
            # 计算偏移量
            offset = (params.page_num - 1) * params.page_size
            
            # 构建查询SQL
            query_sql = """
                SELECT 
                    id, camera_id, device_name, report_date,
                    /* normal_count, warning_count, transition_count,
                    normal_analysis, warning_analysis, transition_analysis, */
                    full_report, created_at, updated_at, submitter,
                    (EXTRACT(YEAR FROM report_date)::text || '年' || 
                     EXTRACT(MONTH FROM report_date)::text || '月' || 
                     EXTRACT(DAY FROM report_date)::text || '日' || 
                     device_name || '巡检日报') AS report_name
                FROM device_daily_report
                WHERE 1=1
            """
            
            params_list = []
            
            # 添加时间范围条件
            if start_date:
                query_sql += " AND report_date >= %s"
                params_list.append(start_date)
            
            if end_date:
                query_sql += " AND report_date <= %s"
                params_list.append(end_date)
            
            # 添加设备ID条件
            if params.camera_ids and len(params.camera_ids) > 0:
                if len(params.camera_ids) == 1:
                    query_sql += " AND camera_id = %s"
                    params_list.append(params.camera_ids[0])
                else:
                    placeholders = ','.join(['%s'] * len(params.camera_ids))
                    query_sql += f" AND camera_id IN ({placeholders})"
                    params_list.extend(params.camera_ids)
            
            # 添加日报名称模糊查询条件
            if params.report_name:
                query_sql += """ AND (EXTRACT(YEAR FROM report_date)::text || '年' || 
                              EXTRACT(MONTH FROM report_date)::text || '月' || 
                              EXTRACT(DAY FROM report_date)::text || '日' || 
                              device_name || '巡检日报') ILIKE %s"""
                params_list.append(f"%{params.report_name}%")
            
            # 添加排序和分页
            query_sql += " ORDER BY report_date DESC, camera_id"
            query_sql += " LIMIT %s OFFSET %s"
            params_list.extend([params.page_size, offset])
            
            # 执行查询
            cur.execute(query_sql, params_list)
            results = cur.fetchall()
            
            # 构建计数查询SQL（用于获取总记录数）
            count_sql = """
                SELECT COUNT(*)
                FROM device_daily_report
                WHERE 1=1
            """
            
            count_params = []
            
            # 添加相同的查询条件
            if start_date:
                count_sql += " AND report_date >= %s"
                count_params.append(start_date)
            
            if end_date:
                count_sql += " AND report_date <= %s"
                count_params.append(end_date)
            
            if params.camera_ids and len(params.camera_ids) > 0:
                if len(params.camera_ids) == 1:
                    count_sql += " AND camera_id = %s"
                    count_params.append(params.camera_ids[0])
                else:
                    placeholders = ','.join(['%s'] * len(params.camera_ids))
                    count_sql += f" AND camera_id IN ({placeholders})"
                    count_params.extend(params.camera_ids)
            
            # 添加日报名称模糊查询条件
            if params.report_name:
                count_sql += """ AND (EXTRACT(YEAR FROM report_date)::text || '年' || 
                              EXTRACT(MONTH FROM report_date)::text || '月' || 
                              EXTRACT(DAY FROM report_date)::text || '日' || 
                              device_name || '巡检日报') ILIKE %s"""
                count_params.append(f"%{params.report_name}%")
            
            # 执行计数查询
            cur.execute(count_sql, count_params)
            total_count = cur.fetchone()[0]
            
            # 构建返回数据
            fields = [
                "id", "camera_id", "device_name", "report_date",
                # "normal_count", "warning_count", "transition_count",
                # "normal_analysis", "warning_analysis", "transition_analysis",
                "full_report", "created_at", "updated_at", "submitter", "report_name"
            ]
            
            result_list = []
            for row in results:
                result_dict = dict(zip(fields, row))
                
                # 确保日期格式正确
                if isinstance(result_dict['report_date'], datetime):
                    result_dict['report_date'] = result_dict['report_date'].strftime('%Y-%m-%d')
                
                if isinstance(result_dict['created_at'], datetime):
                    result_dict['created_at'] = result_dict['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                
                if isinstance(result_dict['updated_at'], datetime):
                    result_dict['updated_at'] = result_dict['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                
                result_list.append(result_dict)
            
            logging.info("query_daily_reports接口请求成功")
            return {
                "code": 200,
                "msg": "查询成功",
                "data": {
                    "rows": result_list,
                    "total": total_count,
                    "page_num": params.page_num,
                    "page_size": params.page_size,
                    "total_pages": (total_count + params.page_size - 1) // params.page_size,
                    "start_date": start_date,
                    "end_date": end_date
                }
            }
            
        except Exception as e:
            logging.error(f"query_daily_reports接口请求失败: {str(e)}")
            return {
                "msg": f"查询失败: {str(e)}",
                "code": 500,
                "data": None
            }
        finally:
            if cur:
                cur.close()
            if conn:
                conn.close()
                
    except Exception as e:
        logging.error(f"query_daily_reports系统错误: {str(e)}")
        return {
            "msg": f"系统错误: {str(e)}",
            "code": 500,
            "data": None
        }

@router.get("/download_report/{report_id}")
async def download_report(report_id: int, background_tasks: BackgroundTasks):
    """
    下载指定ID的日报为Word文档
    
    Args:
        report_id (int): 日报记录的ID
        background_tasks (BackgroundTasks): FastAPI的后台任务对象
        
    Returns:
        FileResponse: Word文档文件响应
    """
    temp_path = None
    try:
        # 在线程池中运行同步的数据库查询，避免阻塞
        result = await run_in_threadpool(
            query_data,
            table="device_daily_report",
            conditions={"id": report_id},
            fields=["camera_id", "device_name", "report_date", "full_report"]
        )
        
        if not result:
            # 使用 HTTPException 返回标准的 404 错误
            raise HTTPException(status_code=404, detail=f"未找到ID为 {report_id} 的报告")
        
        # 解析查询结果
        camera_id, device_name, report_date, full_report = result[0]
        
        # 格式化日期
        if isinstance(report_date, datetime):
            report_date_str = report_date.strftime('%Y-%m-%d')
        else:
            report_date_str = str(report_date)
        
        # 生成报告名称
        year = report_date.year if isinstance(report_date, datetime) else int(report_date_str.split('-')[0])
        month = report_date.month if isinstance(report_date, datetime) else int(report_date_str.split('-')[1])
        day = report_date.day if isinstance(report_date, datetime) else int(report_date_str.split('-')[2])
        report_name = f"{year}年{month}月{day}日{device_name}巡检日报"
        
        # 将动态标题和报告内容组合成一个完整的 Markdown 字符串
        final_markdown = f"# {report_name}\n\n## 巡检场景: {device_name}\n\n---\n\n{full_report}"
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.docx')
        temp_path = temp_file.name
        temp_file.close()  # 关闭文件句柄，以便写入
        
        # 在响应发送后，使用后台任务清理临时文件
        background_tasks.add_task(os.remove, temp_path)
        
        # 尝试使用pypandoc进行高质量转换，如果失败则回退到html2docx
        try:
            # 在线程池中运行 CPU 密集的 pandoc 转换
            await run_in_threadpool(
                pypandoc.convert_text,
                final_markdown,
                'docx',
                format='md',
                outputfile=temp_path
            )
            logging.info("使用pypandoc成功转换文档")
        except Exception as pandoc_error:
            logging.warning(f"pypandoc转换失败，回退到html2docx: {str(pandoc_error)}")
            
            # 回退方案：使用html2docx
            await run_in_threadpool(convert_with_html2docx, final_markdown, temp_path)
            logging.info("使用html2docx回退方案成功转换文档")
        
        # 设置文件名
        filename = f"{report_name}.docx"
        
        # 返回文件响应
        logging.info(f"成功生成报告文档: {filename}")
        return FileResponse(
            path=temp_path,
            filename=filename,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )
        
    except Exception as e:
        # 确保在任何异常情况下，如果临时文件已创建，都能被清理
        if temp_path and os.path.exists(temp_path):
            try:
                os.remove(temp_path)
            except:
                pass
        
        logging.error(f"生成报告文档失败: {str(e)}")
        # 使用 HTTPException 返回标准的 500 错误
        raise HTTPException(status_code=500, detail=f"生成报告文档失败: {str(e)}")


