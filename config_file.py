import os
import sys
import yaml
from dotenv import load_dotenv
import argparse
import logging

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='AI Camera Service')
    
    # 服务器配置
    parser.add_argument('--host', type=str, default='0.0.0.0',
                      help='服务器主机地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=8101,
                      help='服务器端口号 (默认: 8101)')
    
    # 配置文件路径
    parser.add_argument('--env-config', type=str, default='configs/env.yaml',
                      help='环境配置文件路径 (默认: configs/env.yaml)')
    parser.add_argument('--env-file', type=str, default='configs/.env',
                      help='环境变量文件路径 (默认: configs/.env)')
    
    return parser.parse_args()

class Config:
    """配置类，用于统一管理所有配置"""
    _instance = None  # 单例模式的实例存储
    
    def __new__(cls, args=None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, args=None):
        # 确保初始化只执行一次
        if self._initialized:
            return
            
        # 如果没有传入参数，则解析命令行参数
        self.args = args if args is not None else parse_arguments()
        
        # 统一加载环境变量
        self.env_path = self.args.env_file
        load_dotenv(dotenv_path=self.env_path)
        
        # 加载 YAML 配置
        self.env = self.load_yaml_config(self.args.env_config)
        
        # 环境变量
        self.env_vars = dict(os.environ)
        
        # 服务器配置
        self.host = self.args.host
        self.port = self.args.port

        self._initialized = True
    
    @staticmethod
    def load_yaml_config(yaml_file):
        """加载 YAML 配置文件"""
        try:
            with open(yaml_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            # print(f"加载配置文件 {yaml_file} 失败: {str(e)}")
            logging.error(f"加载配置文件 {yaml_file} 失败: {str(e)}")
            return None
            
    @staticmethod
    def load_custom_yaml(yaml_path):
        """加载自定义的 YAML 配置文件"""
        return Config.load_yaml_config(yaml_path)

# 创建全局配置实例
config = Config()

if __name__ == '__main__':
    print(config.host)
    print(config.port)
    print(config.env)
    print(config.env_vars)